# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  suqin.she
@Version        :  V1.0.0
------------------------------------
@File           :  secret.py
@Description    :
@CreateTime     :  2023/8/16 10:22
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/8/16 10:22
"""
import json

import boto3
from botocore.exceptions import ClientError


import json

import boto3
from botocore.exceptions import ClientError


import json
import platform

import boto3
from botocore.exceptions import ClientError


def get_secret():
    check_window = True if platform.system().lower() == 'windows' else False
    if check_window:
        secret = {"db_ec_username": "xxxx", "db_ec_password": "xxxxxx"}
        return secret
    else:
        secret_name = "qa/weee-test"
        region_name = "us-east-2"

        session = boto3.session.Session()  # Create a Secrets Manager client
        client = session.client(service_name='secretsmanager',
                                region_name=region_name)
        try:
            get_secret_value_response = client.get_secret_value(SecretId=secret_name)
        except ClientError as e:
            # For a list of exceptions thrown, see
            # https://docs.aws.amazon.com/secretsmanager/latest/apireference/API_GetSecretValue.html
            raise e
        # Decrypts secret using the associated KMS key.
        secret = json.loads(get_secret_value_response['SecretString'])
        return secret