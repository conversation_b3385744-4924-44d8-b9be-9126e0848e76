"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   mweb_page_category.py
@Description    :  移动端分类页面类
@CreateTime     :  2025/4/25 10:30
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/4/25 10:30
"""

import allure
from playwright.sync_api import Locator

from src.Mweb.EC.conftest import h5_autotest_header
from src.Mweb.EC.mweb_ele.mweb_cart import mweb_cart_ele
from src.Mweb.EC.mweb_ele.mweb_category import mweb_category_ele
from src.Mweb.EC.mweb_ele.mweb_home import mweb_home_ele
from src.Mweb.EC.mweb_pages.mweb_page_home.mweb_page_home import MWebPageHome
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log
from playwright.sync_api import Page
from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_common_page import MWebCommonPage
from src.common.commonui import scroll_one_page_until


class MWebCategorypage(MWebCommonPage):
    def __init__(self, page: Page, header, browser_context, zipcode: str = "98011", page_url=None):
        super().__init__(page, header)
        self.bc = browser_context
        # 进入首页
        self.bc = browser_context
        # 直接进入指定页面
        self.page.goto(TEST_URL + page_url + "?joinEnki=true")
        self.page.wait_for_timeout(2000)

    # def category_filter_delivery_type(self, filter_delivery_type: Locator):
    #     """
    #     该方法包含以下功能：
    #     1. 分类页点击勾选filter_delivery_type对应的filter来筛选不同的搜索结果
    #     """
    #     filter_delivery_type.click()
    #     self.page.wait_for_timeout(2000)

    # def category_filter_delivery_type_check(self, filter_delivery_type: Locator):
    #     """
    #     该方法包含以下功能：
    #     1. 分类页点击勾选filter_delivery_type对应的filter来筛选不同的搜索结果
    #     """
    #     filter_delivery_type.click()
    #     self.page.wait_for_timeout(2000)

    # def category_filter_delivery_type_uncheck(self, filter_delivery_type):
    #     """
    #     该方法包含以下功能：
    #     1. 分类页点击[取消勾选]filter_delivery_type对应的filter，来筛选不同的搜索结果
    #     """
    #     filter_delivery_type.uncheck()
    #     self.page.wait_for_timeout(2000)

    # def category_filter_product_type_check(self, filter_product_type):
    #     """
    #     该方法包含以下功能：
    #     1. 分类页点击勾选filter:Product type, 根据产品类型来筛选产品，获得不同的搜索结果
    #     """
    #     filter_product_type.check()
    #     self.page.wait_for_timeout(2000)

    # def category_filter_product_type_uncheck(self, filter_product_type):
    #     """
    #     该方法包含以下功能：
    #     1. 分类页点击取消勾选filter:Product type, 根据产品类型来筛选产品，获得不同的搜索结果
    #     """
    #     filter_product_type.uncheck()
    #     self.page.wait_for_timeout(2000)

    # def special_category_filter_sub_category(self, sub_category):
    #     """
    #     该方法包含以下功能：
    #     1. 根据传入的特殊分类页点击切换子分类
    #     """
    #     self.FE.ele(sub_category).click()
    #     self.page.wait_for_timeout(2000)

    # def add_product_to_cart(self, product_id):
    #     """
    #     该方法包含以下功能：
    #     1. 根据传入的product_id, 加购指定商品
    #     """
    #     product_id.click()
    #     self.page.wait_for_timeout(2000)

    def go_to_cart_from_category(self):
        """
        该方法包含以下功能：
        1. 点击购物车按钮，进入购物车页面
        """
        self.page.get_by_test_id("wid-mini-cart").click()

    # def _get_cart_count(self):
    #     """获取当前购物车商品数量"""
    #     try:
    #         cart_count_elem = self.page.locator("//span[contains(@class, 'CartIcon_cartIconCountQty')]")
    #         if cart_count_elem.is_visible(timeout=2000):
    #             count_text = cart_count_elem.text_content()
    #             if count_text and count_text.isdigit():
    #                 return int(count_text)
    #     except Exception as e:
    #         log.warning(f"获取购物车数量失败: {str(e)}")
    #     return 0

    # def add_to_alcohol_product_cart_from_category(self):
    #     """
    #     该方法包含以下功能：
    #     1. 分类页加购alcohol类型的商品
    #     """
    #     # 进入deals分类页
    #     self.go_to_special_category_from_hone(mweb_home_ele.ele_deals)
    #     # 滚动到指定位置
    #     scroll_one_page_until(self.page, mweb_category_ele.ele_filter_reset)
    #     # 点击Delivery type = Local Delivery
    #     delivery_type_local = self.page.get_by_test_id(mweb_category_ele.local_delivery_test_id)
    #     self.category_filter_delivery_type(delivery_type_local)
    #
    #     self.page.wait_for_timeout(2000)
    #     product_ids = self.page.query_selector_all("//div[@data-testid='btn-atc-plus']")
    #     assert product_ids, f"本地售卖没有商品"
    #     for index, item in enumerate(product_ids):
    #         try:
    #             self.add_product_to_cart(item)
    #         except Exception as e:
    #             log.info("按钮加购失败" + str(e))
    #         if index == 1:
    #             break
    #
    #     # 滚动到指定位置
    #     scroll_one_page_until(self.page, mweb_category_ele.ele_filter_reset)
    #     # 再调一次，点击取消Delivery type = Local Delivery
    #     self.category_filter_delivery_type(delivery_type_local)
    #     # 点击Delivery type = pantry
    #     delivery_type_pantry = self.page.get_by_test_id(mweb_category_ele.pantry_delivery_test_id)
    #     # 滚动到指定位置
    #     scroll_one_page_until(self.page, mweb_category_ele.ele_filter_reset)
    #     # 点击pantry
    #     self.category_filter_delivery_type(delivery_type_pantry)
    #     # 加购pantry售卖第一个商品
    #     product_ids = self.page.query_selector_all("//div[@data-testid='btn-atc-plus']")
    #     assert product_ids, f"本地售卖没有商品"
    #     for index, item in enumerate(product_ids):
    #         try:
    #             self.add_product_to_cart(item)
    #         except Exception as e:
    #             log.info("按钮加购失败" + str(e))
    #         if index == 1:
    #             break
    #     # 滚动到指定位置
    #     scroll_one_page_until(self.page, mweb_category_ele.ele_filter_reset)
    #     # 再调一次，点击取消Delivery type = pantry
    #     self.category_filter_delivery_type(delivery_type_pantry)
    #
    #     # 点击Delivery type = global
    #     delivery_type_global = self.page.get_by_test_id(mweb_category_ele.global_delivery_test_id)
    #     # 滚动到指定位置
    #     scroll_one_page_until(self.page, mweb_category_ele.ele_filter_reset)
    #     self.category_filter_delivery_type(delivery_type_global)
    #     # 加购global售卖第一个商品
    #     product_ids = self.page.query_selector_all("//div[@data-testid='btn-atc-plus']")
    #     assert product_ids, f"本地售卖没有商品"
    #     for index, item in enumerate(product_ids):
    #         try:
    #             self.add_product_to_cart(item)
    #         except Exception as e:
    #             log.info("按钮加购失败" + str(e))
    #         if index == 1:
    #             break
    #     # 滚动到指定位置
    #     scroll_one_page_until(self.page, mweb_category_ele.ele_filter_reset)
    #     # 再调一次，点击取消Delivery type = global
    #     self.category_filter_delivery_type(delivery_type_global)
    #     # 切换到酒分类
    #     # self.special_category_filter_sub_category(mweb_category_ele.Alcohol)
    #     # 加购商品
    #     product_ids = self.page.query_selector_all("//div[@data-testid='btn-atc-plus']")
    #     assert product_ids, f"本地售卖没有商品"
    #     for index, item in enumerate(product_ids):
    #         try:
    #             self.add_product_to_cart(item)
    #         except Exception as e:
    #             log.info("按钮加购失败" + str(e))
    #         if index == 1:
    #             break
    #
    #     # 8. 去购物车结算
    #     self.go_to_cart_from_category()

    # def add_to_many_cart_from_category(self):
    #     """
    #     该方法包含以下功能：
    #     1. 分类页加购Deals类型的商品
    #     """
    #     # 进入deals分类页
    #     self.go_to_special_category_from_hone(mweb_home_ele.ele_deals)
    #     # 滚动到指定位置
    #     scroll_one_page_until(self.page, mweb_category_ele.ele_filter_reset)
    #     # 点击Delivery type = Local Delivery
    #     delivery_type_local = (self.page.locator(""))
    #     # get_by_test_id(mweb_category_ele.local_delivery_test_id)
    #     self.category_filter_delivery_type(delivery_type_local)
    #
    #     self.page.wait_for_timeout(2000)
    #     product_ids = self.page.query_selector_all("//div[@data-testid='btn-atc-plus']")
    #     assert product_ids, f"本地售卖没有商品"
    #     for index, item in enumerate(product_ids):
    #         try:
    #             self.add_product_to_cart(item)
    #         except Exception as e:
    #             log.info("按钮加购失败" + str(e))
    #         if index == 1:
    #             break
    #
    #     # 滚动到指定位置
    #     scroll_one_page_until(self.page, mweb_category_ele.ele_filter_reset)
    #     # 再调一次，点击取消Delivery type = Local Delivery
    #     self.category_filter_delivery_type(delivery_type_local)
    #     # 点击Delivery type = pantry
    #     delivery_type_pantry = self.page.get_by_test_id(mweb_category_ele.pantry_delivery_test_id)
    #     # 滚动到指定位置
    #     scroll_one_page_until(self.page, mweb_category_ele.ele_filter_reset)
    #     # 点击pantry
    #     self.category_filter_delivery_type(delivery_type_pantry)
    #     # 加购pantry售卖第一个商品
    #     product_ids = self.page.query_selector_all("//div[@data-testid='btn-atc-plus']")
    #     assert product_ids, f"本地售卖没有商品"
    #     for index, item in enumerate(product_ids):
    #         try:
    #             self.add_product_to_cart(item)
    #         except Exception as e:
    #             log.info("按钮加购失败" + str(e))
    #         if index == 1:
    #             break
    #     # 滚动到指定位置
    #     scroll_one_page_until(self.page, mweb_category_ele.ele_filter_reset)
    #     # 再调一次，点击取消Delivery type = pantry
    #     self.category_filter_delivery_type(delivery_type_pantry)
    #
    #     # 点击Delivery type = global
    #     delivery_type_global = self.page.get_by_test_id(mweb_category_ele.ele_global_delivery_xpath)
    #     # 滚动到指定位置
    #     scroll_one_page_until(self.page, mweb_category_ele.ele_filter_reset)
    #     self.category_filter_delivery_type(delivery_type_global)
    #     # 加购global售卖第一个商品
    #     product_ids = self.page.query_selector_all("//div[@data-testid='btn-atc-plus']")
    #     assert product_ids, f"本地售卖没有商品"
    #     for index, item in enumerate(product_ids):
    #         try:
    #             self.add_product_to_cart(item)
    #         except Exception as e:
    #             log.info("按钮加购失败" + str(e))
    #         if index == 1:
    #             break
    #     # 滚动到指定位置
    #     scroll_one_page_until(self.page, mweb_category_ele.ele_filter_reset)
    #     # 再调一次，点击取消Delivery type = global
    #     self.category_filter_delivery_type(delivery_type_global)
    #     # 切换到酒分类
    #     # self.special_category_filter_sub_category(mweb_category_ele.Alcohol)
    #     # 加购商品
    #     product_ids = self.page.query_selector_all("//div[@data-testid='btn-atc-plus']")
    #     assert product_ids, f"本地售卖没有商品"
    #     for index, item in enumerate(product_ids):
    #         try:
    #             self.add_product_to_cart(item)
    #         except Exception as e:
    #             log.info("按钮加购失败" + str(e))
    #         if index == 1:
    #             break
    #
    #     # 8. 去购物车结算
    #     self.go_to_cart_from_category()

    def add_products_from_home_by_filter(self, filter_name, filter_id, count=1):
        """
        通过筛选条件添加商品到购物车
        
        Args:
            filter_name: 筛选条件名称
            filter_id: 筛选条件元素或选择器
            count: 要添加的商品数量
        
        Returns:
            int: 成功添加的商品数量
        """
        try:
            # 点击筛选按钮
            filter_button = self.page.get_by_test_id("btn-sub-category-filter")
            if filter_button.is_visible(timeout=2000):
                filter_button.click()
                self.page.wait_for_timeout(2000)
                log.info("成功点击筛选按钮")

            # 根据filter_id类型处理筛选条件
            if isinstance(filter_id, str):
                # 如果是字符串（选择器），则使用locator方法
                filter_element = self.page.locator(filter_id)
            else:
                # 如果已经是Locator对象，直接使用
                filter_element = filter_id

            # 点击筛选条件
            if filter_element.is_visible(timeout=2000):
                filter_element.click()
                self.page.wait_for_timeout(2000)
                # log.info(f"成功选择{filter_name}筛选条件")
                # self.page.wait_for_timeout(2000)

                # 点击应用按钮
                apply_button = self.page.get_by_test_id("btn-sort-filter-apply")
                if apply_button.is_visible(timeout=2000):
                    apply_button.click()
                    self.page.wait_for_timeout(3000)
                    log.info("成功点击应用按钮")
            else:
                log.error(f"{filter_name}筛选条件不可见")
                return 0

            # 添加商品到购物车
            added_count = 0
            add_buttons = self.page.get_by_test_id("btn-atc-plus").all()

            if not add_buttons:
                log.warning("未找到加购按钮，尝试使用备用定位方式")
                add_buttons = self.page.locator("//div[@data-testid='btn-atc-plus']").all()

            if add_buttons and len(add_buttons) > 0:
                # log.info(f"找到{len(add_buttons)}个加购按钮")

                # 加购商品
                for i, button in enumerate(add_buttons):
                    if i >= count:  # 只加购指定数量的商品
                        break

                    try:
                        # 确保按钮可见
                        button.scroll_into_view_if_needed()
                        self.page.wait_for_timeout(1000)

                        # 点击加购按钮
                        button.click()
                        self.page.wait_for_timeout(2000)
                        added_count += 1
                        log.info(f"成功加购第{i + 1}个商品")
                    except Exception as e:
                        log.error(f"加购第{i + 1}个商品失败: {str(e)}")
            else:
                log.error("未找到任何加购按钮")

            return added_count
        except Exception as e:
            log.error(f"选择{filter_name}筛选条件失败: {str(e)}")
            return 0

    # def add_global_plus_products(self, count=1):
    #     """
    #     筛选并加购Global+商品
    #
    #     Args:
    #         count: 要加购的商品数量，默认为1
    #
    #     Returns:
    #         int: 实际加购的商品数量
    #     """
    #     try:
    #         log.info(f"开始筛选并加购{count}个Global+商品")
    #
    #         # 点击筛选按钮
    #         filter_btn = self.page.get_by_test_id("btn-sub-category-filter")
    #         if filter_btn.is_visible():
    #             filter_btn.click()
    #             self.page.wait_for_timeout(2000)
    #
    #         # 选择Global+筛选条件
    #         global_filter = self.page.locator(mweb_category_ele.ele_global_delivery_xpath)
    #         # if not global_filter.is_visible():
    #         #     # 如果需要，先点击Delivery Type展开选项
    #         #     delivery_type = self.page.locator("//div[text()='Delivery Type']")
    #         #     if delivery_type.is_visible():
    #         #         delivery_type.click()
    #         #         self.page.wait_for_timeout(1000)
    #
    #         # 选择Global+选项
    #         global_filter.click()
    #         self.page.wait_for_timeout(1000)
    #
    #         # 应用筛选
    #         apply_btn = self.page.get_by_test_id("btn-sort-filter-apply")
    #         apply_btn.click()
    #         self.page.wait_for_timeout(3000)
    #
    #         # 等待筛选结果加载
    #         self.page.wait_for_timeout(2000)
    #
    #         # 查找加购按钮
    #         product_buttons = self.page.get_by_test_id("btn-atc-plus").all()
    #         log.info(f"筛选后找到{len(product_buttons)}个Global+商品")
    #
    #         if not product_buttons:
    #             log.warning("未找到Global+商品，筛选可能失败")
    #             return 0
    #
    #         # 加购指定数量的商品
    #         added_count = 0
    #         for index, item in enumerate(product_buttons):
    #             if index >= count:
    #                 break
    #
    #             try:
    #                 log.info(f"尝试加购第{index+1}个Global+商品")
    #                 # 确保元素可见
    #                 item.scroll_into_view_if_needed()
    #                 self.page.wait_for_timeout(1000)
    #
    #                 # 点击加购按钮
    #                 item.click(force=True)  # 使用force=True强制点击
    #                 self.page.wait_for_timeout(2000)
    #                 added_count += 1
    #                 log.info(f"成功加购第{index+1}个Global+商品")
    #             except Exception as e:
    #                 log.error(f"加购第{index+1}个Global+商品失败: {str(e)}")
    #                 # 尝试使用JavaScript点击
    #                 try:
    #                     self.page.evaluate("(element) => element.click()", item)
    #                     self.page.wait_for_timeout(2000)
    #                     added_count += 1
    #                     log.info(f"使用JavaScript成功加购第{index+1}个Global+商品")
    #                 except Exception as js_e:
    #                     log.error(f"使用JavaScript加购也失败: {str(js_e)}")
    #
    #         log.info(f"成功加购{added_count}个Global+商品")
    #         return added_count
    #     except Exception as e:
    #         log.error(f"筛选并加购Global+商品失败: {str(e)}")
    #         import traceback
    #         log.error(traceback.format_exc())
    #         return 0

    def navigate_to_cart_and_checkout(self):
        """
        导航到购物车页面并点击结算按钮
        
        Returns:
            bool: 是否成功跳转到结算页面
        """
        try:
            log.info("开始导航到购物车页面")

            # 点击购物车图标
            cart_icon = self.page.locator("//img[@alt='Go to cart']")
            cart_icon.click()
            self.page.wait_for_timeout(3000)
            log.info("成功进入购物车页面，准备点击结算按钮")

            # 滚动到页面底部找到结算按钮
            self.page.evaluate('window.scrollTo(0, document.body.scrollHeight)')
            self.page.wait_for_timeout(1000)

            # 查找结算按钮
            checkout_btn = self.page.locator("//button[contains(text(), 'Checkout')]")
            if not checkout_btn.is_visible(timeout=2000):
                log.warning("结算按钮不可见，尝试其他定位方式")
                checkout_btn = self.page.locator("//button[contains(text(), 'Checkout')]")

            assert checkout_btn.is_visible(timeout=2000), "结算按钮不可见"

            # 点击结算按钮
            log.info("找到结算按钮，准备点击")
            checkout_btn.click()
            self.page.wait_for_timeout(3000)

            # 验证是否跳转到结算页面
            checkout_url = self.page.url
            log.info(f"当前URL: {checkout_url}")

            is_checkout_page = "/checkout" in checkout_url
            log.info(f"是否成功跳转到结算页面: {is_checkout_page}")

            return is_checkout_page
        except Exception as e:
            log.error(f"导航到购物车并结算失败: {str(e)}")
            import traceback
            log.error(traceback.format_exc())
            return False
    def reset_filter(self):
        """
        重置筛选条件
        """
        with allure.step("重置筛选条件"):
            try:
                log.info("开始重置筛选条件")
                
                # 首先检查是否需要打开筛选面板
                filter_panel_visible = False
                try:
                    # 检查是否有应用按钮可见，如果有，说明筛选面板已打开
                    apply_button = self.page.get_by_test_id("btn-sort-filter-apply")
                    filter_panel_visible = apply_button.is_visible(timeout=1000)
                except:
                    pass
                
                if not filter_panel_visible:
                    # 尝试打开筛选面板
                    filter_button_selectors = [
                        "btn-sub-category-filter",
                        "btn-category-filter",
                        "filter-button",
                        "btn-filter"
                    ]
                    
                    for selector in filter_button_selectors:
                        try:
                            filter_button = self.page.get_by_test_id(selector)
                            if filter_button.is_visible(timeout=2000):
                                filter_button.click()
                                self.page.wait_for_timeout(2000)
                                log.info(f"成功点击筛选按钮: {selector}")
                                break
                        except Exception as e:
                            log.debug(f"使用test-id {selector} 查找筛选按钮失败: {str(e)}")
                
                # 尝试多个可能的重置按钮选择器
                reset_selectors = [
                    "btn-filter-reset",
                    "btn-reset-filter",
                    "//button[contains(text(), 'Reset')]",
                    "//div[contains(text(), 'Reset')]",
                    "//span[contains(text(), 'Reset')]",
                    "//button[contains(@class, 'reset')]",
                    "//div[contains(@class, 'reset')]"
                ]
                
                reset_button_found = False
                for selector in reset_selectors:
                    try:
                        if selector.startswith("//"):
                            reset_button = self.page.locator(selector)
                        else:
                            reset_button = self.page.get_by_test_id(selector)
                        
                        if reset_button.is_visible(timeout=2000):
                            # 确保元素可见
                            try:
                                reset_button.scroll_into_view_if_needed()
                                self.page.wait_for_timeout(1000)
                            except Exception as e:
                                log.warning(f"滚动到重置按钮失败: {str(e)}")
                            
                            # 点击重置按钮
                            reset_button.click()
                            self.page.wait_for_timeout(2000)
                            log.info(f"成功点击重置按钮: {selector}")
                            reset_button_found = True
                            
                            # 检查是否需要点击应用按钮
                            try:
                                apply_button = self.page.get_by_test_id("btn-sort-filter-apply")
                                if apply_button.is_visible(timeout=2000):
                                    apply_button.click()
                                    self.page.wait_for_timeout(3000)
                                    log.info("重置后点击应用按钮")
                            except Exception as e:
                                log.debug(f"重置后点击应用按钮失败: {str(e)}")
                            
                            break
                    except Exception as e:
                        log.debug(f"使用选择器 {selector} 查找重置按钮失败: {str(e)}")
                
                if not reset_button_found:
                    # 如果找不到重置按钮，尝试直接点击应用按钮
                    try:
                        apply_button = self.page.get_by_test_id("btn-sort-filter-apply")
                        if apply_button.is_visible(timeout=2000):
                            apply_button.click()
                            self.page.wait_for_timeout(3000)
                            log.info("未找到重置按钮，直接点击应用按钮")
                        else:
                            # 如果找不到应用按钮，尝试刷新页面
                            log.warning("未找到重置按钮和应用按钮，尝试刷新页面...")
                            self.page.reload()
                            self.page.wait_for_timeout(3000)
                            log.info("页面已刷新")
                    except Exception as e:
                        log.warning(f"点击应用按钮失败: {str(e)}")
                        # 尝试刷新页面
                        self.page.reload()
                        self.page.wait_for_timeout(3000)
                        log.info("页面已刷新")
                
                # 等待筛选条件重置
                self.page.wait_for_timeout(2000)
                log.info("筛选条件已重置")
                
                # 验证筛选条件是否已重置
                try:
                    # 检查URL是否包含筛选参数
                    current_url = self.page.url
                    if "filter_" in current_url:
                        log.warning(f"URL仍包含筛选参数: {current_url}，尝试直接访问无筛选参数的URL")
                        base_url = current_url.split("?")[0]
                        self.page.goto(base_url)
                        self.page.wait_for_timeout(3000)
                        log.info("已直接访问无筛选参数的URL")
                except Exception as e:
                    log.debug(f"验证筛选条件重置失败: {str(e)}")
                
            except Exception as e:
                log.error(f"重置筛选条件失败: {str(e)}")
                import traceback
                log.error(traceback.format_exc())
                
                # 尝试刷新页面作为备选方案
                try:
                    self.page.reload()
                    self.page.wait_for_timeout(3000)
                    log.info("筛选条件重置失败，已刷新页面")
                except Exception as reload_e:
                    log.error(f"刷新页面失败: {str(reload_e)}")

    def add_products_with_filter(self, filter_type, filter_selector, count=1):
        """
        应用筛选条件并加购指定数量的商品
        
        Args:
            filter_type: 筛选类型名称（如"Local Delivery"、"Pantry"等）
            filter_selector: 筛选条件的选择器（XPath或test-id）
            count: 要加购的商品数量
        
        Returns:
            int: 成功加购的商品数量
        """
        with allure.step(f"应用{filter_type}筛选条件并加购{count}个商品"):
            try:
                log.info(f"开始应用{filter_type}筛选条件并加购商品")
                
                # 等待页面加载
                self.page.wait_for_timeout(2000)
                
                # 点击筛选按钮
                filter_button_found = False
                filter_button_selectors = [
                    "btn-sub-category-filter"
                ]
                
                for selector in filter_button_selectors:
                    try:
                        filter_button = self.page.get_by_test_id(selector)
                        if filter_button.is_visible(timeout=2000):
                            filter_button.click()
                            self.page.wait_for_timeout(2000)
                            log.info(f"成功点击筛选按钮: {selector}")
                            filter_button_found = True
                            break
                    except Exception as e:
                        log.debug(f"使用test-id {selector} 查找筛选按钮失败: {str(e)}")

                # if not filter_button_found:
                #     # 尝试使用XPath
                #     xpath_selectors = [
                #         "//button[contains(text(), 'Filter')]",
                #         "//div[contains(text(), 'Filter')]",
                #         "//span[contains(text(), 'Filter')]"
                #     ]
                #
                #     for xpath in xpath_selectors:
                #         try:
                #             filter_button = self.page.locator(xpath)
                #             if filter_button.is_visible(timeout=2000):
                #                 filter_button.click()
                #                 self.page.wait_for_timeout(2000)
                #                 log.info(f"成功点击筛选按钮: {xpath}")
                #                 filter_button_found = True
                #                 break
                #         except Exception as e:
                #             log.debug(f"使用XPath {xpath} 查找筛选按钮失败: {str(e)}")
                #
                if not filter_button_found:
                    log.warning("未找到筛选按钮，尝试直接查找筛选条件")
                #
                # # 选择筛选条件
                # try:
                #     if isinstance(filter_selector, str) and filter_selector.startswith("//"):
                #         filter_condition = self.page.locator(filter_selector)
                #     else:
                #         filter_condition = self.page.get_by_test_id(filter_selector)
                #
                #     if filter_condition.is_visible(timeout=3000):
                #         filter_condition.click()
                #         self.page.wait_for_timeout(2000)
                #         log.info(f"成功点击{filter_type}筛选条件")
                #     else:
                #         # 尝试备选选择器
                #         backup_selectors = []
                #
                #         if filter_type == "Local Delivery":
                #             backup_selectors = [
                #                 "//div[contains(text(), 'Local Delivery')]",
                #                 "//span[contains(text(), 'Local Delivery')]",
                #                 "//label[contains(text(), 'Local Delivery')]",
                #                 "btn-filter-delivery_type-delivery_type_local",
                #                 "btn-delivery_type-delivery_type_local"
                #             ]
                #         elif filter_type == "Pantry":
                #             backup_selectors = [
                #                 "//div[contains(text(), 'Pantry')]",
                #                 "//span[contains(text(), 'Pantry')]",
                #                 "//label[contains(text(), 'Pantry')]",
                #                 "btn-filter-delivery_type-delivery_type_pantry",
                #                 "btn-delivery_type-delivery_type_pantry"
                #             ]
                #
                #         filter_condition_found = False
                #         for selector in backup_selectors:
                #             try:
                #                 if selector.startswith("//"):
                #                     backup_condition = self.page.locator(selector)
                #                 else:
                #                     backup_condition = self.page.get_by_test_id(selector)
                #
                #                 if backup_condition.is_visible(timeout=2000):
                #                     backup_condition.click()
                #                     self.page.wait_for_timeout(2000)
                #                     log.info(f"使用备选选择器 '{selector}' 成功点击{filter_type}筛选条件")
                #                     filter_condition_found = True
                #                     break
                #             except Exception as e:
                #                 log.debug(f"使用备选选择器 '{selector}' 失败: {str(e)}")
                #
                #         if not filter_condition_found:
                #             log.error(f"未找到{filter_type}筛选条件")
                #             return 0
                # except Exception as e:
                #     log.error(f"选择{filter_type}筛选条件失败: {str(e)}")
                #     return 0
                #
                # 点击应用按钮
                try:
                    apply_selectors = [
                        "btn-sort-filter-apply"
                    ]
                    
                    apply_button_found = False
                    for selector in apply_selectors:
                        try:
                            if selector.startswith("//"):
                                apply_button = self.page.locator(selector)
                            else:
                                apply_button = self.page.get_by_test_id(selector)
                            
                            if apply_button.is_visible(timeout=2000):
                                apply_button.click()
                                self.page.wait_for_timeout(3000)
                                log.info(f"成功点击应用按钮: {selector}")
                                apply_button_found = True
                                break
                        except Exception as e:
                            log.debug(f"使用选择器 {selector} 查找应用按钮失败: {str(e)}")
                    
                    if not apply_button_found:
                        log.warning("未找到应用按钮，尝试继续执行...")
                except Exception as e:
                    log.warning(f"点击应用按钮失败: {str(e)}")
                
                # 等待筛选结果加载
                self.page.wait_for_timeout(3000)
                
                # 查找并加购商品
                added_count = self._add_products_to_cart(filter_type, count)
                
                return added_count
                
            except Exception as e:
                log.error(f"应用{filter_type}筛选条件并加购商品失败: {str(e)}")
                import traceback
                log.error(traceback.format_exc())
                return 0

    def _add_products_to_cart(self, filter_type, items_to_add=1):
        """
        查找商品并加购
        
        Args:
            filter_type: 筛选类型名称
            items_to_add: 要加购的商品数量
        
        Returns:
            int: 成功加购的商品数量
        """
        try:
            # 查找可加购的商品
            product_buttons = []
            add_to_cart_selectors = [
                "//div[@data-testid='btn-atc-plus']",
                "//button[contains(@class, 'add-to-cart')]",
                "//div[contains(@class, 'add-to-cart')]",
                "//i[@data-role='addButtonPlusIcon']",
                "//div[contains(@class, 'AddToCartButton')]"
            ]
            
            for selector in add_to_cart_selectors:
                try:
                    buttons = self.page.query_selector_all(selector)
                    if buttons and len(buttons) > 0:
                        product_buttons = buttons
                        log.info(f"使用选择器 '{selector}' 找到{len(buttons)}个加购按钮")
                        break
                except Exception as e:
                    log.debug(f"使用选择器 '{selector}' 查找加购按钮失败: {str(e)}")
            
            if not product_buttons:
                log.error(f"未找到可加购的{filter_type}商品")
                return 0
            
            log.info(f"找到{len(product_buttons)}个可加购的{filter_type}商品")
            
            # 加购指定数量的商品
            added_count = 0
            for index, item in enumerate(product_buttons):
                if index >= items_to_add:
                    break
                try:
                    # 确保元素可见
                    try:
                        item.scroll_into_view_if_needed()
                        self.page.wait_for_timeout(1000)
                    except Exception as e:
                        log.warning(f"滚动到元素失败: {str(e)}")
                    
                    # 尝试点击
                    try:
                        item.click()
                        self.page.wait_for_timeout(2000)
                        added_count += 1
                        log.info(f"成功加购第{index + 1}个{filter_type}商品")
                    except Exception as e:
                        log.warning(f"点击加购按钮失败，尝试使用JavaScript: {str(e)}")
                        # 尝试使用JavaScript点击
                        try:
                            self.page.evaluate("(element) => element.click()", item)
                            self.page.wait_for_timeout(2000)
                            added_count += 1
                            log.info(f"使用JavaScript成功加购第{index + 1}个{filter_type}商品")
                        except Exception as js_e:
                            log.error(f"使用JavaScript加购也失败: {str(js_e)}")
                except Exception as e:
                    log.error(f"加购第{index + 1}个{filter_type}商品失败: {str(e)}")
            
            log.info(f"成功加购{added_count}个{filter_type}商品")
            return added_count
            
        except Exception as e:
            log.error(f"查找并加购{filter_type}商品失败: {str(e)}")
            return 0

    def verify_cart_items(self):
        """
        验证购物车中的商品
        Returns:
            bool: 验证是否成功
        """
        with allure.step("验证购物车中的商品"):
            try:
                # 等待购物车商品加载
                self.page.wait_for_selector(mweb_cart_ele.ele_cart_normal_card, timeout=5000)

                # 获取购物车商品
                cart_items = self.page.query_selector_all(mweb_cart_ele.ele_cart_normal_card)
                if not cart_items:
                    log.error("购物车中没有商品")
                    return False

                log.info(f"购物车中有{len(cart_items)}个商品")
                return True

            except Exception as e:
                log.error(f"验证购物车商品失败: {str(e)}")
                return False


    def apply_multiple_filters_and_add_products(self, filters_config):
        """
        应用多个筛选条件并加购商品

        Args:
            filters_config: 筛选配置列表，每个配置包含 filter_type, test_id, items_to_add
                           例如: [{"filter_type": "Local Delivery", "test_id": "btn-filter-delivery_type-delivery_type_local", "items_to_add": 2}]
                           如果需要使用XPath选择器，可以直接在test_id中传入XPath字符串，例如: "//div[contains(@class, 'box-border')]"

        Returns:
            dict: 每种筛选类型成功加购的商品数量
        """
        with allure.step("应用多个筛选条件并加购商品"):
            try:
                results = {}
                total_added = 0

                for i, config in enumerate(filters_config):
                    filter_type = config["filter_type"]
                    test_id = config["test_id"]
                    items_to_add = config.get("items_to_add", 1)

                    # 最后一个筛选条件不需要重置
                    reset_after = i < len(filters_config) - 1

                    log.info(f"开始应用第{i + 1}个筛选条件: {filter_type}")
                    added = self.add_products_with_filter(self.page, filter_type, test_id)


                    results[filter_type] = added
                    total_added += added

                    log.info(f"完成第{i + 1}个筛选条件: {filter_type}，加购{added}个商品")

                log.info(f"所有筛选条件应用完成，总共加购{total_added}个商品")
                return results

            except Exception as e:
                log.error(f"应用多个筛选条件并加购商品失败: {str(e)}")
                import traceback
                log.error(traceback.format_exc())
                return {}
