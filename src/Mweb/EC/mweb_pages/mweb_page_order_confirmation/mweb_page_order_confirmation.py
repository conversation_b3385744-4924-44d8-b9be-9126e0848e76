"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   mweb_page_order_confirmation.py
@Description    :  
@CreateTime     :  2025/6/18 11:52
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/6/8 11:52
"""
from playwright.sync_api import Page, expect

from src.Mweb.EC.mweb_ele.mweb_home import mweb_home_ele
from src.Mweb.EC.mweb_ele.mweb_order_confirmation import mweb_order_confirmation_ele
from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_common_page import MWebCommonPage
from src.api.zipcode import switch_zipcode
from src.common.commonui import close_advertise_on_home
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log


class MWebOrderComfirmationPage(MWebCommonPage):
    """订单确认页面类"""

    def __init__(self, page: Page, header, browser_context, zipcode: str = "98011", page_url: str = None):
        """
        构造方法主要包含以下功能：
        1. 进入订单成功页面
        2.如果zipcode不是98011，则切换zipcode
        3.调用close_advertise_on_home(self.page)关掉首页的广告
        """
        super().__init__(page, header)
        self.bc = browser_context
        # 直接进入订单成功页
        self.page.goto(TEST_URL + page_url)

        # 获取顶部语言
        if not self.page.get_by_test_id("wid-language").locator(
                "//span[text()='English'and contains(@class,'Header')]").all():
            # 切换为英文
            pass
        close_advertise_on_home(self.page)

    def start_earning(self):
        """
        点击开始赚取积分按钮，打开分享弹窗
        """
        # 点击开始赚取积分按钮
        start_button = self.FE.ele(mweb_order_confirmation_ele.ele_start_earning)
        assert start_button.is_visible(), "开始赚取积分按钮不可见"
        start_button.click()
        self.page.wait_for_timeout(2000)
        share_link = self.page.locator("//input[contains(@value, '/order/share/')]").first
        if share_link.is_visible():
            share_url = share_link.get_attribute("value")
            log.info(f"分享链接: {share_url}")

            # 验证链接格式是否正确
            assert "/order/share/grocery/view/" in share_url, "分享链接格式不正确"
            log.info("分享链接格式正确")

    def verify_action_buttons(self):
        """验证操作按钮"""
        # 验证订单详情按钮
        order_details_btn = self.FE.ele(mweb_order_confirmation_ele.ele_order_details)
        assert order_details_btn.is_visible(), "订单详情按钮不可见"
        log.info(f"订单详情按钮: {order_details_btn.text_content()}")
        
        # 验证分享赚取积分按钮
        share_btn = self.FE.ele(mweb_order_confirmation_ele.ele_start_earning)
        assert share_btn.is_visible(), "分享赚取积分按钮不可见"
        log.info(f"分享赚取积分按钮: {share_btn.text_content()}")
        
        # 验证推荐商品区域
        recommendations = self.FE.ele(mweb_order_confirmation_ele.ele_recommendations)
        assert recommendations.is_visible(), "推荐商品区域不可见"
        log.info(f"推荐商品区域: {recommendations.text_content()}")
        
        return {
            "order_details": order_details_btn,
            "share_btn": share_btn,
            "recommendations": recommendations
        }
