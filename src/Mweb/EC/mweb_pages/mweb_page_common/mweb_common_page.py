from src.common.commonui import FindElement
from playwright.sync_api import Page
from src.config.weee.log_help import log


class MWebCommonPage:
    def __init__(self, page: Page, header):
        self.page = page
        self.header = header
        self.FE = FindElement(self.page)

    def home_page_switch_zipcode(self, zipcode="98011"):
        self.page.get_by_test_id("wid-modal-zip-code").click()
        self.page.wait_for_timeout(2000)
        self.page.locator("input[value]").fill(zipcode)
        self.page.get_by_test_id("wid-btn-zipcode-btn").click()
        self.page.wait_for_timeout(3000)


    def home_page_common_check(self):
        """
        首页需要校验的公共组件
        """
        pass


    def cart_page_common_check(self):
        pass


    def pdp_mweb_page_common_check(self):
        assert self.page.get_by_test_id("wid-pdp-thumbnail-0").is_visible()
        assert self.page.get_by_test_id("wid-pdp-zoom-img").is_visible()
        assert self.page.get_by_test_id("btn-favorite").all()[0].is_visible()
        assert self.page.get_by_test_id("pdp-detail-main").is_visible()
    def pdp_page_common_check(self):
        assert self.page.get_by_test_id("wid-mweb_page_pdp-thumbnail-0").is_visible()
        assert self.page.get_by_test_id("wid-mweb_page_pdp-zoom-img").is_visible()
        assert self.page.get_by_test_id("btn-favorite").all()[0].is_visible()
        assert self.page.get_by_test_id("mweb_page_pdp-detail-main").is_visible()

    def add_pdp_related_products_to_cart(self, ele_pdp_related_add_to_cart_button, n):
        for index, item in enumerate(ele_pdp_related_add_to_cart_button):
            try:
                item.evaluate('(item) => item.click()')
                # item.click()
            except Exception as e:
                log.info("加购按钮点击失败" + str(e))
            # self.page.reload()
            self.page.wait_for_timeout(1000)
            if index == n:
                break


    def category_page_common_check(self):
        pass


    def checkout_page_common_check(self):
        pass

    def scroll_to_pos(self,pos):
        """
        滚动到指定模块
        """
        self.page.locator(pos).scroll_into_view_if_needed()
        self.page.wait_for_timeout(1000)






