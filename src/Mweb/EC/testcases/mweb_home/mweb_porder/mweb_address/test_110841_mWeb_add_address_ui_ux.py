import allure
import pytest

from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele.mweb_home.mweb_porder.mweb_address import mweb_address_ele
from src.Mweb.EC.mweb_pages.mweb_page_cart.mweb_page_cart import MWebCartPage
from src.Mweb.EC.mweb_pages.mweb_page_home.mweb_page_porder.mweb_page_address.mweb_page_address import MWebPageAddress
from src.common.commonui import scroll_one_page_until
from src.config.weee.log_help import log

@allure.story("【110841】首页/checkout/acccount/order detail-新增&应用地址验证")
class TestMWebAddAddressUIUX:
    pytestmark = [pytest.mark.h5order, pytest.mark.mweb_todo, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("【110841】 从首页-添加地址验证")
    def test_110841_mWeb_add_address_from_home_page_ui_ux(self, phone_page: dict,
                                                          h5_autotest_header,
                                                          h5_open_and_close_trace):
        """
        【110841】 从首页-添加地址验证
        1. 点击首页zipcode，data-testid= "wid-modal-zip-code-and-eta" ，弹出Deliver to pop
        2.点击新增地址按钮 deliver_to_pop_add_btn,弹出delivery address pop
        4. 在delivery address pop 里输入 “18607 Bothell Way NE, Bothell, WA 98011”
        5. 地址会联想，点击第一个地址，进入 Delivery Info 新增地址pop
        3.按照页面输入框，输入 姓名、电话
        4.其中街道、城市、zipcode 会默认已经填上了
        4. 点击保存，回到首页
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")

        # 直接进入指定页面
        address_page = MWebPageAddress(p, h5_autotest_header, browser_context=c)
        p.wait_for_timeout(3000)
        # 新增前先删除地址
        address_page.delete_address_from_home()

        address_page.add_address_from_home(address="18607 Bothell Way NE, Bothell, WA 98011")
        # 验证是否回到首页
        log.info("验证是否成功回到首页")
        p.wait_for_timeout(3000)
        # 可以通过检查首页特有的元素来验证
        home_indicator = p.get_by_test_id("wid-home-collection-cm_categories")  # 首页banner按钮
        assert home_indicator.is_visible(), "未成功回到首页"
        log.info("从首页添加地址测试完成")



    @allure.title("【110841】 从Account页-设置页面-添加地址验证")
    def test_110841_mWeb_add_address_from_account_setting_page_ui_ux(self, phone_page: dict,
                                                          h5_autotest_header,
                                                          h5_open_and_close_trace):
        """
        【110841】 从Account页-设置页面-添加地址验证
        1. 进入 /account/settings 页面
        3.点击address book 地址管理，进入地址簿 /account/address?source=me_setting 页面
        4.滚动到最后，点击新增地址 Add a new address
        5.进入Delivery Address pop
        6.在delivery address pop 里输入 “18607 Bothell Way NE, Bothell, WA 98011”输入地址
        7.地址会联想，点击第一个地址，进入 Delivery Info 新增地址pop
        8.按照页面输入框，输入 姓名、电话、备注
        9. 其中街道、城市、zipcode 会默认已经填上了
        9.点击保存，回到 address book页面
        10.验证地址是否添加成功
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        # 直接进入指定页面
        address_page = MWebPageAddress(p, h5_autotest_header, browser_context=c, page_url="/account/settings")
        p.wait_for_timeout(2000)
        # 1. 点击address book 地址管理，进入地址簿 /account/address?source=me_setting 页面
        p.get_by_test_id("")
        p.wait_for_timeout(1000)
        # 有地址
        if p.get_by_test_id("wid-address-item").is_visible():
            # 有地址
            # 滚动到指定位置
            scroll_one_page_until(p, p.get_by_test_id("add adress"))
            # 点击新增地址 Add a new address
            p.get_by_test_id("add adress").click()
            p.wait_for_timeout(2000)
            # 2. 点击新增地址按钮 deliver_to_pop_add_btn,弹出delivery address pop
            log.info("步骤2：点击新增地址按钮")
            add_address_btn = p.get_by_test_id(mweb_address_ele.deliver_to_pop_add_btn)
            assert add_address_btn.is_visible(), "新增地址按钮不可见"
            add_address_btn.click()
            p.wait_for_timeout(3000)

            # 3-4. 使用公共方法输入地址并选择联想地址
            log.info("步骤3-4：输入地址并选择第一个联想地址")
            assert address_page.input_address_and_select_suggestion(
                "18607 Bothell Way NE, Bothell, WA 98011"), "输入地址并选择联想地址失败"

            # 5. 使用公共方法填写配送地址表单
            log.info("步骤5：填写配送地址表单")
            assert address_page.fill_delivery_address_form("Test", "Automation", "3467534557"), "填写配送地址表单失败"

            # 6. 点击保存，回到首页
            log.info("步骤6：点击保存按钮")
            save_btn = p.get_by_test_id(mweb_address_ele.address_save_button)
            assert save_btn.is_visible(), "保存按钮不可见"
            save_btn.click()
            p.wait_for_timeout(2000)

            # 验证添加地址成功且回到地址簿页面
            log.info("验证添加地址成功且回到地址簿页面")
            assert p.get_by_test_id("address")
            # 点击地址卡片应用地址，回到首页
            p.get_by_test_id("address").click()
            p.wait_for_timeout(2000)
            assert p.get_by_test_id("wid-main-banner-card-0").is_visible(), "未成功回到首页"
        else:
            # 直接进入的是delivery info页面
            assert p.get_by_test_id("wid-input-last-name").is_visible()
            # 5. 使用公共方法填写配送地址表单
            log.info("步骤5：填写配送地址表单")
            assert address_page.fill_delivery_address_form("Test", "Automation", "3467534557"), "填写配送地址表单失败"

            # 6. 点击保存，回到设置页面
            log.info("步骤6：点击保存按钮")
            p.get_by_test_id(mweb_address_ele.address_save_button)
            p.wait_for_timeout(2000)
            assert p.get_by_test_id("Address Book").is_visible()
            log.info("地址簿页面没有地址，新增地址")


    @allure.title("【110841】 从结算页面-添加地址验证")
    def test_110841_mWeb_add_address_from_checkout_page_ui_ux(self, phone_page: dict,
                                                          h5_autotest_header,
                                                          h5_open_and_close_trace):
        """
        【110841】 从结算页面-添加地址验证
        1. 进入购物车页面
        3.如果购物车为空，点击加购商品
        4.如果有商品，直接点击结算按钮
        5.如果有中间页，点击continue
        6.如果有upsell，点击continue
        7.进入结算页，点击地址选择区域
        8.点击新增地址 Add a new address
        5.进入Delivery Address pop
        6.在delivery address pop 里输入 “18607 Bothell Way NE, Bothell, WA 98011”输入地址
        7.地址会联想，点击第一个地址，进入 Delivery Info 新增地址pop
        8.按照页面输入框，输入 姓名、电话、备注
        9. 其中街道、城市、zipcode 会默认已经填上了
        9.点击保存，回到 address book页面
        10.验证地址是否添加成功
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        # 直接进入指定页面
        cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url="/cart")
        p.wait_for_timeout(2000)
        # 1. 如果购物车为空，点击加购商品
        if p.get_by_test_id("btn-cart-start-shopping").is_visible():
            # 滚动到指定位置
            scroll_one_page_until(p, p.get_by_test_id("wid-cart-recommend-tab-perference_cart"))
            # 点击猜你喜欢商品
            cart_page.add_recommend_product_product(2)
            p.wait_for_timeout(2000)
        else:
            # 2. 点击结算按钮
            p.get_by_test_id("btn-checkout").click()
            # 如果中间页存在，点击继续
            if p.get_by_test_id("wid-popup-cart1").is_visible():
                # 点击全选
                p.get_by_test_id("btn-select-all-carts").click()
                p.wait_for_timeout(1000)
                # 再点击结算
                p.get_by_test_id("btn-select-cart-checkout")
                p.wait_for_timeout(5000)

                p.get_by_test_id("wid-popup-cart1").click()
            p.wait_for_timeout(1000)
            # 有地址
            if p.get_by_test_id("wid-address-item").is_visible():
                # 有地址
                # 滚动到指定位置
                scroll_one_page_until(p, p.get_by_test_id("add adress"))
                # 点击新增地址 Add a new address
                p.get_by_test_id("add adress").click()
                p.wait_for_timeout(2000)
                # 2. 点击新增地址按钮 deliver_to_pop_add_btn,弹出delivery address pop
                log.info("步骤2：点击新增地址按钮")
                add_address_btn = p.get_by_test_id(mweb_address_ele.deliver_to_pop_add_btn)
                assert add_address_btn.is_visible(), "新增地址按钮不可见"
                add_address_btn.click()
                p.wait_for_timeout(3000)

                # 3-4. 使用公共方法输入地址并选择联想地址
                log.info("步骤3-4：输入地址并选择第一个联想地址")
                assert cart_page.input_address_and_select_suggestion(
                    "18607 Bothell Way NE, Bothell, WA 98011"), "输入地址并选择联想地址失败"

                # 5. 使用公共方法填写配送地址表单
                log.info("步骤5：填写配送地址表单")
                assert cart_page.fill_delivery_address_form("Test", "Automation", "3467534557"), "填写配送地址表单失败"

                # 6. 点击保存，回到首页
                log.info("步骤6：点击保存按钮")
                save_btn = p.get_by_test_id(mweb_address_ele.address_save_button)
                assert save_btn.is_visible(), "保存按钮不可见"
                save_btn.click()
                p.wait_for_timeout(2000)

                # 验证添加地址成功且回到地址簿页面
                log.info("验证添加地址成功且回到地址簿页面")
                assert p.get_by_test_id("address")
                # 点击地址卡片应用地址，回到首页
                p.get_by_test_id("address").click()
                p.wait_for_timeout(2000)
                assert p.get_by_test_id("wid-main-banner-card-0").is_visible(), "未成功回到首页"
            else:
                # 直接进入的是delivery info页面
                assert p.get_by_test_id("wid-input-last-name").is_visible()
                # 5. 使用公共方法填写配送地址表单
                log.info("步骤5：填写配送地址表单")
                assert cart_page.fill_delivery_address_form("Test", "Automation", "3467534557"), "填写配送地址表单失败"

                # 6. 点击保存，回到设置页面
                log.info("步骤6：点击保存按钮")
                p.get_by_test_id(mweb_address_ele.address_save_button)
                p.wait_for_timeout(2000)
                assert p.get_by_test_id("Address Book").is_visible()
                log.info("地址簿页面没有地址，新增地址")
