import allure
import pytest

from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele.mweb_home.mweb_porder.mweb_address import mweb_address_ele
from src.Mweb.EC.mweb_pages.mweb_page_cart.mweb_page_cart import MWebCartPage
from src.Mweb.EC.mweb_pages.mweb_page_home.mweb_page_porder.mweb_page_address.mweb_page_address import MWebPageAddress
from src.common.commonui import scroll_one_page_until
from src.config.weee.log_help import log

@allure.story("【110834】 首页/checkout/acccount/order detail-删除地址验证")
class TestMWebAddAddressUIUX:
    pytestmark = [pytest.mark.h5order, pytest.mark.mweb_regression, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("【110834】 从首页-删除地址验证")
    def test_110834_mWeb_delete_address_from_home_page_ui_ux(self, phone_page: dict,
                                                          h5_autotest_header,
                                                          h5_open_and_close_trace):
        """
        【110834】 从首页-删除地址验证
        1. 点击首页zipcode，data-testid= "wid-modal-zip-code-and-eta" ，弹出Deliver to pop
        2.点击边界地址按钮 deliver_to_pop_add_btn,弹出delivery address pop
        4. 在delivery address pop 里输入 “18607 Bothell Way NE, Bothell, WA 98011”
        5. 地址会联想，点击第一个地址，进入 Delivery Info 新增地址pop
        3.按照页面输入框，输入 姓名、电话
        4.其中街道、城市、zipcode 会默认已经填上了
        4. 点击保存，回到首页
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")

        # 直接进入指定页面
        address_page = MWebPageAddress(p, h5_autotest_header, browser_context=c)
        p.wait_for_timeout(3000)
        address_page.delete_address_from_home()
        address_page.add_address_from_home(address="1020 E Florence Ave, Los Angeles, CA 90001")





