import allure
import pytest
from playwright.sync_api import Page
from src.config.weee.log_help import log
from src.Mweb.EC.mweb_ele.mweb_home.mweb_home_ele import *


@allure.story("H5首页活动弹窗测试")
class TestMWebHomeActivityPopup:
    pytestmark = [pytest.mark.mweb_regression]

    @allure.title("H5首页活动弹窗功能完整流程测试")
    @pytest.mark.h5home
    def test_108263_mweb_home_activity_popup_flow(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        测试H5首页活动弹窗完整流程
        1. 访问首页检查popup接口调用
        2. 如果popup_exist=true，测试弹窗点击跳转
        3. 如果popup_exist=false，切换store测试
        4. 依次测试Chinese、Japanese、Korean store
        5. 所有store都没有popup则跳过
        """
        test_page: Page = phone_page["page"]
        log.info("开始测试H5首页活动弹窗完整流程")

        # 监听popup接口调用
        popup_responses = []
        def handle_response(response):
            if "/ec/activity/popup/page" in response.url and "page=page_home" in response.url:
                try:
                    response_data = response.json()
                    popup_responses.append({
                        'url': response.url,
                        'data': response_data,
                        'popup_exist': response_data.get('object', {}).get('popup_exist', False)
                    })
                    log.info(f"捕获到popup接口: popup_exist={response_data.get('object', {}).get('popup_exist', False)}")
                except Exception as e:
                    log.warning(f"无法解析popup接口响应: {str(e)}")

        test_page.on("response", handle_response)

        # 1. 访问首页
        test_page.goto("https://www.sayweee.com/en")
        test_page.wait_for_timeout(8000)
        test_page.wait_for_load_state("networkidle", timeout=30000)

        # 关闭可能的广告弹窗
        self.close_advertisement_popup(test_page)

        # 2. 检查初始popup状态
        initial_popup_exist = self.check_popup_api_response(popup_responses)
        log.info(f"初始页面popup状态: {initial_popup_exist}")

        if initial_popup_exist:
            # 3. 如果有popup，测试点击跳转功能
            log.info("检测到初始页面popup，开始测试点击跳转功能")
            click_result = self.popup_click_navigation(test_page)
            if click_result:
                log.info("✅ 初始页面popup点击成功，页面已跳转，测试用例通过")
                return  # 点击成功才退出
            else:
                log.warning("⚠️ 初始页面popup点击失败，开始store切换测试")
                # 点击失败，继续尝试store切换
                self.store_switching_for_popup(test_page, popup_responses)
        else:
            # 4. 如果没有popup，开始store切换测试
            log.info("未检测到初始popup，开始store切换测试")
            self.store_switching_for_popup(test_page, popup_responses)

        log.info("H5首页活动弹窗完整流程测试完成")

    def check_popup_api_response(self, popup_responses):
        """检查popup接口响应中的popup_exist状态"""
        if popup_responses:
            latest_response = popup_responses[-1]
            return latest_response.get('popup_exist', False)
        return False

    def popup_click_navigation(self, page):
        """测试popup点击跳转功能"""
        try:
            log.info("开始检查popup是否显示...")

            # 尝试多种方式定位popup元素
            popup_selectors = [
                ele_mweb_home_activity_popup_image,  # 主要选择器
                "//img[@data-testid='wid-activity-image-picture']",  # 备用选择器1
                "[data-testid='wid-activity-image-picture']",  # 备用选择器2
                "img[data-testid='wid-activity-image-picture']"  # 备用选择器3
            ]

            popup_element = None
            for selector in popup_selectors:
                try:
                    element = page.locator(selector).first
                    if element.is_visible(timeout=3000):
                        popup_element = element
                        log.info(f"✅ 使用选择器找到popup: {selector}")
                        break
                except:
                    continue

            if not popup_element:
                log.warning("❌ popup未显示，尝试所有选择器都失败")
                # 打印页面上所有可能的popup相关元素
                try:
                    all_testid_elements = page.locator("[data-testid*='activity']").all()
                    log.info(f"页面上找到 {len(all_testid_elements)} 个包含'activity'的元素")
                    for i, elem in enumerate(all_testid_elements[:5]):  # 只显示前5个
                        try:
                            testid = elem.get_attribute("data-testid")
                            log.info(f"  元素{i+1}: data-testid='{testid}'")
                        except:
                            pass
                except:
                    pass
                return False

            # 记录当前URL
            current_url = page.url
            log.info(f"📍 点击popup前URL: {current_url}")

            # 确保元素可点击
            popup_element.scroll_into_view_if_needed()
            page.wait_for_timeout(1000)

            # 点击popup
            log.info("🖱️ 开始点击popup...")
            popup_element.click()

            # 等待页面跳转，增加等待时间
            log.info("⏳ 等待页面跳转...")
            page.wait_for_timeout(8000)  # 增加等待时间到8秒

            # 等待页面加载完成
            try:
                page.wait_for_load_state("networkidle", timeout=10000)
            except:
                log.warning("等待networkidle超时，继续验证跳转")

            # 验证页面跳转
            new_url = page.url
            log.info(f"📍 点击popup后URL: {new_url}")

            if current_url != new_url:
                log.info("✅ popup点击跳转功能测试通过，已成功跳转到落地页")
                return True
            else:
                log.warning("⚠️ 点击popup后页面未跳转，URL保持不变")
                return False

        except Exception as e:
            log.error(f"❌ popup点击跳转测试失败: {str(e)}")
            return False

    def store_switching_for_popup(self, page, popup_responses):
        """测试store切换寻找popup - 一旦发现popup就停止切换"""
        test_stores = [
            {"name": "Chinese", "selector": ele_mweb_home_store_chinese},
            {"name": "Japanese", "selector": ele_mweb_home_store_japanese},
            {"name": "Korean", "selector": ele_mweb_home_store_korean}
        ]
        popup_found = False
        popup_clicked_successfully = False
        tested_stores = []
        store_switched = False

        # 依次测试每个store，一旦发现popup就停止
        for store_info in test_stores:
            store_name = store_info["name"]
            store_selector = store_info["selector"]

            log.info(f"开始切换并测试{store_name} store")
            tested_stores.append(store_name)
            store_switched = True

            # 清空之前的响应记录
            # popup_responses.clear()

            # 切换store
            switch_success = self.switch_store(page, store_name, store_selector)
            if not switch_success:
                log.warning(f"切换{store_name} store失败，继续测试下一个")
                continue

            # 等待页面加载和接口调用
            log.info(f"等待{store_name} store页面加载和接口调用...")
            page.wait_for_timeout(5000)
            page.wait_for_load_state("networkidle", timeout=15000)

            # 检查popup状态
            popup_exist = self.check_popup_api_response(popup_responses)
            log.info(f"{store_name} store popup状态: {popup_exist}")

            if popup_exist:
                popup_found = True
                log.info(f"✅ 在{store_name} store下发现popup！开始测试点击功能")

                # 测试popup点击功能
                click_result = self.popup_click_navigation(page)
                if click_result:
                    popup_clicked_successfully = True
                    log.info(f"✅ {store_name} store popup点击成功，页面已跳转")
                else:
                    log.warning(f"⚠️ {store_name} store popup点击失败，但popup已弹出")

                # 重要：一旦发现popup就立即停止，不再测试其他store
                log.info(f"🎯 已在{store_name} store发现popup，停止测试其他store")
                break
            else:
                log.info(f"❌ {store_name} store未发现popup，继续测试下一个")

        # 最终结果判断
        log.info("=" * 50)
        log.info("测试结果汇总:")
        log.info(f"已测试的store: {tested_stores}")
        log.info(f"store切换状态: {store_switched}")
        log.info(f"popup发现状态: {popup_found}")
        log.info(f"popup点击状态: {popup_clicked_successfully}")
        log.info("=" * 50)

        if store_switched and popup_found:
            # 情况1：切换过store并且已经弹出了popup，case就算通过
            log.info("🎉 测试结果：切换store后发现popup，测试用例通过")
        elif store_switched and not popup_found:
            # 情况2：切换了所有store都没有弹出popup，就跳过这条case
            log.warning("⚠️ 测试结果：切换了所有store都未发现popup")
            pytest.skip(f"切换了所有store {tested_stores} 都未发现popup，跳过此测试用例。原因：可能之前已经弹过了或当前时间段没有活动")
        else:
            # 没有进行store切换的情况（理论上不会到这里）
            log.warning("⚠️ 测试结果：未进行store切换")
            pytest.skip("未进行store切换，跳过此测试用例")

    def switch_store(self, page, store_name, store_selector):
        """切换store"""
        try:
            log.info(f"开始切换到{store_name} store")

            # 1. 点击store按钮
            try:
                store_button = page.locator(ele_mweb_home_store_button).first
                store_button.wait_for(state="visible", timeout=10000)
                store_button.click()
                page.wait_for_timeout(2000)
            except:
                log.error(f"store按钮不可见或点击失败")
                return False

            # 2. 选择指定的store
            try:
                store_option = page.locator(store_selector).first
                store_option.wait_for(state="visible", timeout=5000)
                store_option.click()
                page.wait_for_timeout(3000)
            except:
                log.error(f"{store_name} store选项不可见或点击失败")
                return False

            log.info(f"切换到{store_name} store完成")
            return True

        except Exception as e:
            log.error(f"切换{store_name} store失败: {str(e)}")
            return False

    def close_advertisement_popup(self, page):
        """关闭广告弹窗"""
        try:
            ad_close = page.locator("//div[@class='ant-modal-body']//img[@alt='Close']").first
            if ad_close.is_visible(timeout=3000):
                ad_close.click()
                page.wait_for_timeout(2000)
                log.info("关闭了广告弹窗")
        except:
            log.info("没有广告弹窗需要关闭")

    @allure.title("H5 store切换功能独立测试")
    @pytest.mark.h5home
    def test_store_switch_functionality(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        独立测试store切换功能
        """
        test_page: Page = phone_page["page"]
        log.info("开始测试H5 store切换功能")

        # 访问首页
        test_page.goto("https://www.sayweee.com/en")
        test_page.wait_for_timeout(8000)
        test_page.wait_for_load_state("networkidle", timeout=30000)

        # 关闭广告弹窗
        self.close_advertisement_popup(test_page)

        # 测试切换所有store
        test_stores = [
            {"name": "Chinese", "selector": ele_mweb_home_store_chinese},
            {"name": "Japanese", "selector": ele_mweb_home_store_japanese},
            {"name": "Korean", "selector": ele_mweb_home_store_korean}
        ]

        for store_info in test_stores:
            switch_result = self.switch_store(test_page, store_info["name"], store_info["selector"])
            assert switch_result, f"切换{store_info['name']} store失败"
            log.info(f"成功切换到{store_info['name']} store")

        log.info("H5 store切换功能测试完成")

    @allure.title("H5 popup接口监听测试")
    @pytest.mark.h5home
    def test_popup_api_monitoring(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        独立测试popup接口监听功能
        """
        test_page: Page = phone_page["page"]
        log.info("开始测试H5 popup接口监听")

        popup_api_calls = []
        def handle_response(response):
            if "/ec/activity/popup/page" in response.url and "page=page_home" in response.url:
                popup_api_calls.append(response.url)
                log.info(f"监听到popup接口调用: {response.url}")

        test_page.on("response", handle_response)

        # 访问首页
        test_page.goto("https://www.sayweee.com/en")
        test_page.wait_for_timeout(8000)
        test_page.wait_for_load_state("networkidle", timeout=30000)

        # 验证是否监听到接口调用
        if len(popup_api_calls) > 0:
            log.info(f"成功监听到 {len(popup_api_calls)} 次popup接口调用")
        else:
            log.warning("未监听到popup接口调用")