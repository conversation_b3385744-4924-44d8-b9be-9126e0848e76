{"name": "H5 store切换功能独立测试", "status": "passed", "description": "\n        独立测试store切换功能\n        ", "start": 1752820145557, "stop": 1752820171144, "uuid": "af28b526-c2f4-45e5-8cc0-dec99feab265", "historyId": "03345d2c835f86797d5937fafb2b048b", "testCaseId": "03345d2c835f86797d5937fafb2b048b", "fullName": "src.Mweb.EC.testcases.mweb_home.mweb_popup.test_108263_mweb_home_popup.TestMWebHomeActivityPopup#test_store_switch_functionality", "labels": [{"name": "story", "value": "H5首页活动弹窗测试"}, {"name": "tag", "value": "h5home"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "mweb_regression"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_home.mweb_popup"}, {"name": "suite", "value": "test_108263_mweb_home_popup"}, {"name": "subSuite", "value": "TestMWebHomeActivityPopup"}, {"name": "host", "value": "SHLAP10453"}, {"name": "thread", "value": "5696-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_home.mweb_popup.test_108263_mweb_home_popup"}]}