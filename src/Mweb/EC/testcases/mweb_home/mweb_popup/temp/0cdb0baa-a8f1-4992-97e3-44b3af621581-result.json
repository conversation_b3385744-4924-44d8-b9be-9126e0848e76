{"name": "H5 popup接口监听测试", "status": "passed", "description": "\n        独立测试popup接口监听功能\n        ", "start": 1752820173847, "stop": 1752820182882, "uuid": "60e29bb2-0abb-4b69-ac48-7532d191de45", "historyId": "9a09a562af2f6b36ffc5431567db7e95", "testCaseId": "9a09a562af2f6b36ffc5431567db7e95", "fullName": "src.Mweb.EC.testcases.mweb_home.mweb_popup.test_108263_mweb_home_popup.TestMWebHomeActivityPopup#test_popup_api_monitoring", "labels": [{"name": "story", "value": "H5首页活动弹窗测试"}, {"name": "tag", "value": "h5home"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "mweb_regression"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_home.mweb_popup"}, {"name": "suite", "value": "test_108263_mweb_home_popup"}, {"name": "subSuite", "value": "TestMWebHomeActivityPopup"}, {"name": "host", "value": "SHLAP10453"}, {"name": "thread", "value": "5696-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_home.mweb_popup.test_108263_mweb_home_popup"}]}