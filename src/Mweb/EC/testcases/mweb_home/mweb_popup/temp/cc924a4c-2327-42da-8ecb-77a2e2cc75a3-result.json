{"name": "H5 store切换功能独立测试", "status": "failed", "statusDetails": {"message": "AssertionError: 切换Korean store失败\nassert False", "trace": "self = <src.Mweb.EC.testcases.mweb_home.mweb_popup.test_108263_mweb_home_popup.TestMWebHomeActivityPopup object at 0x000001CDDCE88FD0>\nphone_page = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppData\\Lo...-1091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en?grocery-store=ja'>}\nh5_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...N143WqbOQqcGgB3ujDcDjb_mAohZsAgKOGNqbyAZ-LE31UpmHOHSYIyWMyd_mrMYSy_0SXvThFnAK1uSrI-G44EedsHSbx80oava9ydGJSYGEPeU', ...}\nh5_open_and_close_trace = None\n\n    @allure.title(\"H5 store切换功能独立测试\")\n    @pytest.mark.h5home\n    def test_store_switch_functionality(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):\n        \"\"\"\n        独立测试store切换功能\n        \"\"\"\n        test_page: Page = phone_page[\"page\"]\n        log.info(\"开始测试H5 store切换功能\")\n    \n        # 访问首页\n        test_page.goto(\"https://www.sayweee.com/en\")\n        test_page.wait_for_timeout(8000)\n        test_page.wait_for_load_state(\"networkidle\", timeout=30000)\n    \n        # 关闭广告弹窗\n        self.close_advertisement_popup(test_page)\n    \n        # 测试切换所有store\n        test_stores = [\n            {\"name\": \"Chinese\", \"selector\": ele_mweb_home_store_chinese},\n            {\"name\": \"Japanese\", \"selector\": ele_mweb_home_store_japanese},\n            {\"name\": \"Korean\", \"selector\": ele_mweb_home_store_korean}\n        ]\n    \n        for store_info in test_stores:\n            switch_result = self.switch_store(test_page, store_info[\"name\"], store_info[\"selector\"])\n>           assert switch_result, f\"切换{store_info['name']} store失败\"\nE           AssertionError: 切换Korean store失败\nE           assert False\n\ntest_108263_mweb_home_popup.py:309: AssertionError"}, "description": "\n        独立测试store切换功能\n        ", "start": 1752820936748, "stop": 1752820987729, "uuid": "10dac2ff-8006-4e51-a1da-f730eb82deb2", "historyId": "03345d2c835f86797d5937fafb2b048b", "testCaseId": "03345d2c835f86797d5937fafb2b048b", "fullName": "src.Mweb.EC.testcases.mweb_home.mweb_popup.test_108263_mweb_home_popup.TestMWebHomeActivityPopup#test_store_switch_functionality", "labels": [{"name": "story", "value": "H5首页活动弹窗测试"}, {"name": "tag", "value": "h5home"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "mweb_regression"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_home.mweb_popup"}, {"name": "suite", "value": "test_108263_mweb_home_popup"}, {"name": "subSuite", "value": "TestMWebHomeActivityPopup"}, {"name": "host", "value": "SHLAP10453"}, {"name": "thread", "value": "15376-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_home.mweb_popup.test_108263_mweb_home_popup"}]}