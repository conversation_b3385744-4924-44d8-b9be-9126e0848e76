{"name": "H5首页活动弹窗功能完整流程测试", "status": "passed", "description": "\n        测试H5首页活动弹窗完整流程\n        1. 访问首页检查popup接口调用\n        2. 如果popup_exist=true，测试弹窗点击跳转\n        3. 如果popup_exist=false，切换store测试\n        4. 依次测试Chinese、Japanese、Korean store\n        5. 所有store都没有popup则跳过\n        ", "start": 1752820133547, "stop": 1752820145150, "uuid": "8cbb373d-f1f2-445a-a823-b645f4b6e392", "historyId": "d6f7ac568ca6bb78d28575e67a447948", "testCaseId": "d6f7ac568ca6bb78d28575e67a447948", "fullName": "src.Mweb.EC.testcases.mweb_home.mweb_popup.test_108263_mweb_home_popup.TestMWebHomeActivityPopup#test_108263_mweb_home_activity_popup_flow", "labels": [{"name": "story", "value": "H5首页活动弹窗测试"}, {"name": "tag", "value": "h5home"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "mweb_regression"}, {"name": "parentSuite", "value": "src.Mweb.EC.testcases.mweb_home.mweb_popup"}, {"name": "suite", "value": "test_108263_mweb_home_popup"}, {"name": "subSuite", "value": "TestMWebHomeActivityPopup"}, {"name": "host", "value": "SHLAP10453"}, {"name": "thread", "value": "5696-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Mweb.EC.testcases.mweb_home.mweb_popup.test_108263_mweb_home_popup"}]}