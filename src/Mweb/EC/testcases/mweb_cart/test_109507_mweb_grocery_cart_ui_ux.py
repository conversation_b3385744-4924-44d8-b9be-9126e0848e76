"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   test_109507_mweb_grocery_cart_ui_ux.py
@Description    :  H5购物车-生鲜购物车UI/UX验证
@CreateTime     :  2025/4/21 14:22
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/4/21 14:22
"""
import allure
import pytest
from playwright.sync_api import Page
from src.Mweb.EC.mweb_ele.mweb_cart import mweb_cart_ele
from src.Mweb.EC.mweb_ele.mweb_category import mweb_category_ele
from src.Mweb.EC.mweb_pages.mweb_page_cart.mweb_page_cart import MWebCartPage
from src.Mweb.EC.mweb_pages.mweb_page_category.mweb_page_category import MWebCategorypage
from src.api.zipcode import switch_zipcode
from src.common.commfunc import empty_cart
from src.common.commonui import scroll_one_page_until
from src.config.weee.log_help import log



@allure.story("H5购物车-生鲜购物车UI/UX验证")
class TestMwebGroceryCartUIUX:
    pytestmark = [pytest.mark.mweb_regression, pytest.mark.zhuli]
    @allure.title("H5生鲜购物车UI自动化验证")
    @pytest.mark.fresh
    def test_109507_mweb_grocery_cart_ui(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        生鲜购物车UI自动化验证:
        1. 调用切换zipcode接口切换到98011
        2. 使用封装方法加购Local Delivery商品
        3. 遍历购物车商品
        4. 获取购物车商品标题和价格(带$符号)
        5. 验证结算流程
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")

        # 1. 准备测试环境
        with allure.step("准备测试环境"):
            # 切换zipcode到98011
            log.info("切换zipcode到98011")
            res = switch_zipcode(h5_autotest_header, "98011")
            assert res.get('object') == 'Success', f"切换zipcode失败，res={res}"
            p.wait_for_timeout(2000)
            # 清空购物车
            # MWebCartPage.cart_page_operations()
            try:
                empty_cart(h5_autotest_header)
                log.info("清空购物车成功")
                p.wait_for_timeout(2000)
            except Exception as e:
                log.info(f"清空购物车发生异常: {str(e)}")
                p.wait_for_timeout(3000)

        # 2. 使用封装方法加购Local Delivery商品
        with allure.step("使用封装方法加购Local Delivery商品"):
            # 创建分类页面对象
            category_page = MWebCategorypage(p, h5_autotest_header, browser_context=c, page_url="/category/sale")
            p.wait_for_timeout(2000)
            
            # 检查筛选按钮是否存在
            filter_button = p.get_by_test_id("btn-sub-category-filter")
            log.info(f"筛选按钮是否可见: {filter_button.is_visible(timeout=1000)}")
            
            # 检查Local Delivery元素选择器
            # log.info(f"Local Delivery元素选择器: {mweb_category_ele.ele_local_delivery}")
            
            # 尝试直接使用XPath定位Local Delivery选项
            local_filter_id =p.locator(mweb_category_ele.ele_local_delivery_xpath)
            
            # 调用封装方法加购Local Delivery商品
            added_count = category_page.add_products_from_home_by_filter(
                filter_name="Local Delivery",
                filter_id=local_filter_id,
                count=2,  # 加购2个商品
            )
            p.wait_for_timeout(5000)  # 增加等待时间
            log.info(f"成功加购{added_count}个商品")
            assert added_count > 0, "未能成功加购商品"

        # 3. 进入购物车页面
        with allure.step("进入购物车页面"):
            # 创建购物车页面对象
            cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url="/cart")
            p.wait_for_timeout(2000)
            
            # 关闭可能出现的广告弹窗
            if p.locator("//img[contains(@aria-label, 'close button')]").all():
                p.locator("//img[contains(@aria-label, 'close button')]").click()
                log.info("关闭广告弹窗")

        # 4. 验证购物车UI
        with allure.step("验证购物车UI"):
            # 滚动回购物车顶部
            log.info("滚动回购物车顶部")
            scroll_one_page_until(p, mweb_cart_ele.ele_cart_normal)
            assert cart_page.FE.ele(mweb_cart_ele.ele_cart_normal).is_visible(), "生鲜购物车不存在"
            p.wait_for_timeout(2000)

            # 判断生鲜购物车的标题=Local delivery
            assert "Local delivery" == cart_page.FE.ele(mweb_cart_ele.ele_cart_grocery_top_title).text_content(), "购物车标题不正确"
            # 判断只有购物车标题下面的文案显示正确
            assert "Delivered by Weee! Truck" == cart_page.FE.ele(mweb_cart_ele.ele_grocery_cart_text).text_content(), "购物车配送文案不正确"

            # 验证购物车商品
            with allure.step("验证购物车商品"):
                # 使用合并后的方法验证购物车商品
                assert cart_page.verify_cart_items(cart_type="normal"), "购物车商品验证失败"

            # 执行稍后再买操作
            with allure.step("执行稍后再买操作"):
                assert cart_page.save_for_later_operations(cart_type="normal"), "稍后再买操作失败"

            p.wait_for_timeout(2000)
            # 执行购物车商品删除操作
            with allure.step("执行商品删除操作"):
                assert cart_page.remove_cart_item(cart_type="normal"), "商品删除操作失败"



        # # 6. 进入购物车页面并点击结算
        # with allure.step("进入购物车页面并点击结算"):
        #     # 使用封装的方法导航到购物车并点击结算
        #     checkout_success = category_page.navigate_to_cart_and_checkout()
        #
        #     assert checkout_success, "未能成功跳转到结算页面"
        #     log.info("成功跳转到结算页面")
        #
        # # 7. 验证结算页面
        # with allure.step("验证结算页面"):
        #     # 验证结算页面标题
        #     checkout_header = p.locator(mweb_checkout_ele.ele_checkout_title)
        #     assert checkout_header.is_visible(timeout=2000), "结算页面标题不可见"
        #
        #     # 验证结算页面地址信息部分
        #     address_section = p.locator(mweb_checkout_ele.ele_delivery_info_section)
        #     assert address_section.is_visible(timeout=2000), "结算页面地址信息部分不可见"
        #
        #     log.info("成功验证结算页面内容")

        log.info("生鲜购物车UI自动化验证完成")
