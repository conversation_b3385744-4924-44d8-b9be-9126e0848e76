import allure
import pytest
from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele.mweb_cart import mweb_cart_ele
from src.Mweb.EC.mweb_ele.mweb_cart import mweb_trade_in_ele
from src.Mweb.EC.mweb_ele.mweb_cart import mweb_recommendations_ele
from src.Mweb.EC.mweb_pages.mweb_page_cart.mweb_page_cart import MWebCartPage
from src.Mweb.EC.mweb_pages.mweb_page_cart.mweb_trade_in_page import MWebTradeInPage
from src.common.commonui import scroll_one_page_until
from src.config.weee.log_help import log
from src.common.commfunc import empty_cart, CommonCheck


@allure.story("【107320】 $35<X<$68-换购页面UI/UX验证")
class TestMWebTradeInUIUX:
    pytestmark = [pytest.mark.h5cart, pytest.mark.mweb_todo, pytest.mark.transaction]
    @allure.title("【107320-1】 金额小于$35-换购模块验证")
    def test_107320_mWeb_trade_in_less_35_ui_ux(self, phone_page: dict, h5_autotest_header):
        """
        【107320-1】 金额小于$35-换购模块验证
        测试步骤：
        1. 清空购物车后从推荐模块加购商品
        2. 确保购物车金额小于$35
        3. 验证不显示购物车换购模块
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")

        # 初始化页面和清空购物车
        try:
            CommonCheck().set_porder(h5_autotest_header, 98011)
            empty_cart(h5_autotest_header)
        except Exception as e:
            log.info(f"初始化失败: {str(e)}")

        # 初始化页面对象
        cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url="/cart")

        # 1. 从推荐模块加购商品
        scroll_one_page_until(p, mweb_cart_ele.ele_recommend_tab)
        recommend_card = cart_page.FE.eles(mweb_cart_ele.ele_recommend_module_card)

        # 加购一个商品
        if len(recommend_card) > 0:
            recommend_card[0].query_selector(u"//div[@data-testid='btn-atc-plus']").click()
            p.wait_for_timeout(3000)

            # 回到购物车第一个商品位置
            p.query_selector(f"{mweb_cart_ele.ele_cart_normal_card}" + "[1]").scroll_into_view_if_needed()
            p.wait_for_timeout(2000)

            # 获取购物车金额
            total_fee = cart_page.FE.ele(mweb_cart_ele.ele_cart_normal_total + u"//span[2]").text_content().split("$")[
                1]
            log.info(f"当前购物车金额: ${total_fee}")

            # 如果金额大于等于$35，移除商品
            while float(total_fee) >= 35:
                # 移除商品
                cart_page.FE.ele(mweb_cart_ele.ele_cart_normal_card + u"//div[@data-testid='btn-remove']").click()
                p.wait_for_timeout(2000)

                # 如果购物车为空，重新加购
                if not cart_page.FE.ele(mweb_cart_ele.ele_cart_normal_card).is_visible():
                    recommend_card[0].query_selector(u"//div[@data-testid='btn-atc-plus']").click()
                    p.wait_for_timeout(3000)
                    p.query_selector(f"{mweb_cart_ele.ele_cart_normal_card}" + "[1]").scroll_into_view_if_needed()
                    p.wait_for_timeout(2000)

                # 重新获取金额
                total_fee = \
                cart_page.FE.ele(mweb_cart_ele.ele_cart_normal_total + u"//span[2]").text_content().split("$")[1]
                log.info(f"调整后购物车金额: ${total_fee}")

            # 断言金额小于$35
            assert float(total_fee) < 35, f"购物车金额应小于$35，实际为${total_fee}"

            # 断言不显示购物车换购模块
            assert not cart_page.FE.ele(mweb_cart_ele.ele_cart_trade_in).is_visible(), "金额小于$35时不应显示换购模块"
            log.info("验证金额小于$35时不显示换购模块成功")
    pytestmark = [pytest.mark.h5cart, pytest.mark.mweb_todo, pytest.mark.transaction]
    @allure.title("【107320-2】 金额在$35-$68之间-换购页面UI/UX验证")
    def test_107320_mWeb_trade_in_35_to_68_ui_ux(self, phone_page: dict, h5_autotest_header):
        """
        【107320-2】 金额在$35-$68之间-换购页面UI/UX验证
        测试步骤：
        1. 清空购物车后从推荐模块加购商品
        2. 确保购物车金额在$35-$68之间
        3. 验证显示购物车换购模块
        4. 验证换购卡片上不可加购
        5. 点击查看更多，进入换购页面
        6. 验证换购页面UI元素和文案
        7. 验证换购商品不可加购
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")

        # 初始化页面和清空购物车
        try:
            CommonCheck().set_porder(h5_autotest_header, 98011)
            empty_cart(h5_autotest_header)
        except Exception as e:
            log.info(f"初始化失败: {str(e)}")

        # 初始化页面对象
        cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url="/cart")
        trade_in_page = MWebTradeInPage(p)

        # 1. 从推荐模块加购商品
        scroll_one_page_until(p, mweb_cart_ele.ele_recommend_tab)
        recommend_card = cart_page.FE.eles(mweb_cart_ele.ele_recommend_module_card)
        cart_amount = 0

        # 加购商品直到金额在$35-$68之间
        for index, item in enumerate(recommend_card):
            if index >= 20:  # 限制循环次数
                break

            # 加购推荐商品
            item.query_selector(u"//div[@data-testid='btn-atc-plus']").click()
            p.wait_for_timeout(3000)

            # 回到购物车第一个商品位置
            p.query_selector(f"{mweb_cart_ele.ele_cart_normal_card}" + "[1]").scroll_into_view_if_needed()
            p.wait_for_timeout(2000)

            # 获取购物车金额
            total_fee = cart_page.FE.ele(mweb_cart_ele.ele_cart_normal_total + u"//span[2]").text_content().split("$")[
                1]
            cart_amount = float(total_fee)
            log.info(f"当前购物车金额: ${cart_amount}")

            # 如果金额在$35-$68之间，跳出循环
            if 35 <= cart_amount < 68:
                break

            # 如果金额大于等于$68，移除一些商品
            if cart_amount >= 68:
                while cart_amount >= 68:
                    # 移除商品
                    cart_page.FE.ele(mweb_cart_ele.ele_cart_normal_card + u"//div[@data-testid='btn-remove']").click()
                    p.wait_for_timeout(2000)

                    # 重新获取金额
                    total_fee = \
                    cart_page.FE.ele(mweb_cart_ele.ele_cart_normal_total + u"//span[2]").text_content().split("$")[1]
                    cart_amount = float(total_fee)
                    log.info(f"调整后购物车金额: ${cart_amount}")

                # 如果金额在$35-$68之间，跳出循环
                if 35 <= cart_amount < 68:
                    break

        # 断言金额在$35-$68之间
        assert 35 <= cart_amount < 68, f"购物车金额应在$35-$68之间，实际为${cart_amount}"
        log.info(f"确认购物车金额在$35-$68之间: ${cart_amount}")

        # 断言换购模块存在
        assert cart_page.FE.ele(mweb_cart_ele.ele_cart_trade_in).is_visible(), "金额在$35-$68之间时应显示换购模块"

        # 断言换购卡片上不可加购
        assert cart_page.FE.ele(
            mweb_cart_ele.ele_cart_trade_in_card + u"//div[contains(@class,'product-card-btn-shadow')]").is_visible(), "金额在$35-$68之间时换购卡片应不可加购"

        # 点击查看更多，跳转换购页
        cart_page.FE.ele(mweb_cart_ele.ele_cart_trade_in_button).click()
        p.wait_for_timeout(2000)

        # 断言进入换购页面
        assert cart_page.FE.ele(mweb_cart_ele.ele_trade_in).is_visible(), "应成功进入换购页面"

        # 断言换购页面上方的文案
        discount_wrap = cart_page.FE.ele(mweb_cart_ele.ele_trade_in_banner)

        # 断言 "Up to $25+ off!" 文本存在
        up_to_25_off_text = discount_wrap.query_selector('text="Up to $25+ off!"')
        assert up_to_25_off_text, "换购页面应显示'Up to $25+ off!'文案"

        # 断言 "Enjoy discounts only if you add items from this list to your cart." 文本存在
        enjoy_discounts_text = discount_wrap.query_selector(
            'text="Enjoy discounts only if you add items from this list to your cart."')
        assert enjoy_discounts_text, "换购页面应显示'Enjoy discounts only if you add items from this list to your cart.'文案"

        # 断言换购页面不可加购
        trade_in_card = cart_page.FE.eles(mweb_cart_ele.ele_trade_in_card)
        assert len(trade_in_card) > 0, "换购页面应显示换购商品"

        # 验证换购商品卡片元素和不可加购状态
        item = trade_in_card[0]

        # 断言卡片基本信息
        assert item.query_selector(u"//div[@data-role='image-container']//img"), "换购商品卡片应显示商品图片"

        # 定位折扣标签
        discount_label = item.query_selector('span:has-text("% OFF")')
        assert discount_label, "换购商品卡片应显示折扣标签"

        # 定位产品标题
        title = item.query_selector(mweb_cart_ele.ele_trade_in_card_title)
        product_title = title.query_selector(u'[@data-role="product-title"]')
        assert product_title, "换购商品卡片应显示产品标题"

        # 定位价格信息
        price = item.query_selector(mweb_cart_ele.ele_trade_in_card_price)
        current_price = price.query_selector(u'[@data-role="text"]')
        assert current_price, "换购商品卡片应显示当前价格"

        # 定位划线价格
        base_price = price.query_selector('.enki-body-sm.text-surface-100-fg-minor.line-through')
        assert base_price, "换购商品卡片应显示划线价格"

        # 点击不可加购按钮，验证行为
        item.query_selector(u"//button").click()
        p.wait_for_timeout(1000)

        log.info("验证金额在$35-$68之间时换购页面UI/UX成功")

    pytestmark = [pytest.mark.h5cart, pytest.mark.mweb_todo, pytest.mark.transaction]
    @allure.title("【107320-3】 金额大于$68-换购模块验证")
    def test_107320_mWeb_trade_in_more_68_ui_ux(self, phone_page: dict, h5_autotest_header):
        """
        【107320-3】 金额大于$68-换购模块验证
        测试步骤：
        1. 清空购物车后从推荐模块加购商品
        2. 确保购物车金额大于$68
        3. 验证显示购物车换购模块
        4. 验证换购卡片上可加购
        5. 点击加购换购商品
        6. 验证换购商品加入购物车成功
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")

        # 初始化页面和清空购物车
        try:
            CommonCheck().set_porder(h5_autotest_header, 98011)
            empty_cart(h5_autotest_header)
        except Exception as e:
            log.info(f"初始化失败: {str(e)}")

        # 初始化页面对象
        cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url="/cart")

        # 1. 从推荐模块加购商品
        scroll_one_page_until(p, mweb_cart_ele.ele_recommend_tab)
        recommend_card = cart_page.FE.eles(mweb_cart_ele.ele_recommend_module_card)
        cart_amount = 0

        # 加购商品直到金额大于$68
        for index, item in enumerate(recommend_card):
            if index >= 20:  # 限制循环次数
                break

            # 加购推荐商品
            item.query_selector(u"//div[@data-testid='btn-atc-plus']").click()
            p.wait_for_timeout(3000)

            # 回到购物车第一个商品位置
            p.query_selector(f"{mweb_cart_ele.ele_cart_normal_card}" + "[1]").scroll_into_view_if_needed()
            p.wait_for_timeout(2000)

            # 获取购物车金额
            total_fee = cart_page.FE.ele(mweb_cart_ele.ele_cart_normal_total + u"//span[2]").text_content().split("$")[
                1]
            cart_amount = float(total_fee)
            log.info(f"当前购物车金额: ${cart_amount}")

            # 如果金额大于$68，跳出循环
            if cart_amount >= 68:
                break

        # 断言金额大于等于$68
        assert cart_amount >= 68, f"购物车金额应大于等于$68，实际为${cart_amount}"
        log.info(f"确认购物车金额大于等于$68: ${cart_amount}")

        # 断言换购模块存在
        assert cart_page.FE.ele(mweb_cart_ele.ele_cart_trade_in).is_visible(), "金额大于$68时应显示换购模块"

        # 断言换购卡片上可加购
        assert cart_page.FE.ele(mweb_cart_ele.ele_cart_trade_in_card_add).is_visible(), "金额大于$68时换购卡片应可加购"

        # 记录当前购物车商品数量
        cart_items_before = len(cart_page.FE.eles(mweb_cart_ele.ele_cart_normal_card))

        # 点击加购换购商品
        cart_page.FE.ele(mweb_cart_ele.ele_cart_trade_in_card_add).click()
        p.wait_for_timeout(3000)

        # 断言换购商品加入购物车成功
        cart_items_after = len(cart_page.FE.eles(mweb_cart_ele.ele_cart_normal_card))
        assert cart_items_after > cart_items_before, "换购商品应成功加入购物车"
        log.info(f"换购商品成功加入购物车，购物车商品数量从{cart_items_before}增加到{cart_items_after}")

        log.info("验证金额大于$68时换购模块功能成功")

    pytestmark = [pytest.mark.h5cart, pytest.mark.mweb_todo, pytest.mark.transaction]
    @allure.title("【107320】 $35<X<$68-换购页面UI/UX验证")
    def test_107320_mWeb_trade_in_35_x_68_ui_ux(self, phone_page: dict, h5_autotest_header):
        """
        【107320】 $35<X<$68-换购页面UI/UX验证
        测试步骤：
        1. 清空购物车后从推荐模块加购商品
        2. 验证购物车换购模块状态
        3. 进入换购页面，验证按钮状态和提示信息
        4. 进入凑单页面，验证各项功能
        5. 完成凑单后返回购物车，验证状态
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")

        # 初始化页面和清空购物车
        try:
            CommonCheck().set_porder(h5_autotest_header, 98011)
            empty_cart(h5_autotest_header)
        except Exception as e:
            log.info(f"初始化失败: {str(e)}")

        # 初始化页面对象
        cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url="/cart")
        trade_in_page = MWebTradeInPage(p)

        # 1. 从推荐模块加购商品
        scroll_one_page_until(p, mweb_cart_ele.ele_recommend_tab)
        recommend_card = cart_page.FE.eles(mweb_cart_ele.ele_recommend_module_card)

        for index1, item in enumerate(recommend_card) :
            item.query_selector(u"//div[@data-testid='btn-atc-plus']").click()
            p.wait_for_timeout(3000)
            # 回到购物车第一个商品位置
            p.query_selector(f"{mweb_cart_ele.ele_cart_normal_card}" + "[1]").scroll_into_view_if_needed()
            p.wait_for_timeout(2000)
            total_fee = cart_page.FE.ele(mweb_cart_ele.ele_cart_normal_total + u"//span[2]").text_content().split("$")[
                1]
            print(total_fee)
            # 如果商品金额小于35，不显示购物车换购模块
            if float(total_fee) < 35:
                # ele_cart_trade_in = cart_page.FE.ele(mweb_cart_ele.ele_cart_trade_in)
                assert not cart_page.FE.ele(mweb_cart_ele.ele_cart_trade_in)
            # 如果商品金额大于35，小于68，显示购物车换购模块
            elif 35 <= float(total_fee) < 68:
                # 断言换购模块存在
                assert cart_page.FE.ele(mweb_cart_ele.ele_cart_trade_in).is_visible()
                # 断言 换购卡片上不可加购
                assert cart_page.FE.ele(
                    mweb_cart_ele.ele_cart_trade_in_card + u"//div[contains(@class,'product-card-btn-shadow')]").is_visible()
                # 点击查看更多，跳转换购页，然后再返回购物车
                cart_page.FE.ele(mweb_cart_ele.ele_cart_trade_in_button).click()
                # 断言进入换购页面
                assert cart_page.FE.ele(mweb_cart_ele.ele_trade_in).is_visible()
                # 断言换购页面上方的文案
                discount_wrap = cart_page.FE.ele(mweb_cart_ele.ele_trade_in_banner)
                # 断言 "Up to $25+ off!" 文本存在
                up_to_25_off_text = discount_wrap.query_selector('text="Up to $25+ off!"')
                assert up_to_25_off_text
                # 断言 "Enjoy discounts only if you add items from this list to your cart." 文本存在
                enjoy_discounts_text = discount_wrap.query_selector(
                    'text="Enjoy discounts only if you add items from this list to your cart."')
                assert enjoy_discounts_text

                # 断言换购页面不可加购
                trade_in_card = cart_page.FE.eles(mweb_cart_ele.ele_trade_in_card)
                # 点击不可加购按钮，弹出pop 提示
                for index2, item2 in enumerate(trade_in_card):
                    # 断言卡片基本信息
                    assert item2.query_selector(u"//div[@data-role='image-container']//img")
                    # 定位折扣标签
                    discount_label = item2.query_selector('span:has-text("% OFF")')
                    assert discount_label
                    title = item2.query_selector(mweb_cart_ele.ele_trade_in_card_title)
                    # 定位产品标题
                    product_title = title.query_selector(u'[@data-role="product-title"]')
                    assert product_title
                    price = item2.query_selector(mweb_cart_ele.ele_trade_in_card_price)
                    # 定位当前价格
                    current_price = price.query_selector(u'[@data-role="text"]')
                    assert current_price
                    # 定位划线价格
                    base_price = price.query_selector('.enki-body-sm.text-surface-100-fg-minor.line-through')
                    assert base_price
                    # 定位搜藏按钮并点击
                    item2.query_selector(u"//div[@data-testid='btn-favorite']").click()
                    # 订单select按钮并点击
                    item2.query_selector(u"//button").click()
                    # assert p.query_selector()
                    # 点击商品卡片

                    if index2 == 0:
                        break

            # 如果商品金额大于68，换购可加购
            elif float(total_fee) >= 68:
                assert cart_page.FE.ele(mweb_cart_ele.ele_cart_trade_in).is_visible()
                # 断言 换购卡片上可加购
                assert cart_page.FE.ele(mweb_cart_ele.ele_cart_trade_in_card_add).is_visible()
                # 点击加购换购商品
                cart_page.FE.ele(mweb_cart_ele.ele_cart_trade_in_card_add).click()
                # 断言换购商品加入购物车成功
            if index1 == 20:
                break
