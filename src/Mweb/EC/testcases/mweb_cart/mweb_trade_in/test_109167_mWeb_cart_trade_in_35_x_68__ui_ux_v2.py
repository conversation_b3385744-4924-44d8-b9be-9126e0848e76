import allure
import pytest

from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele.mweb_cart import mweb_cart_ele
from src.Mweb.EC.mweb_pages.mweb_page_cart.mweb_page_cart import MWebCartPage
from src.common.commonui import scroll_one_page_until
from src.config.weee.log_help import log
from src.common.commfunc import empty_cart, CommonCheck


@allure.story("【109167】 购物车-换购模块加购金额验证")
class TestMWebCartTradeIn:
    def setup_cart_page(self, phone_page, h5_autotest_header):
        """
        初始化购物车页面
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        # 如果不是98011，切换回98011
        try:
            CommonCheck().set_porder(h5_autotest_header, 98011)
        except Exception as e:
            log.info("账号没有在98011下" + str(e))
        try:
            empty_cart(h5_autotest_header)
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))
        # 进入指定页面
        cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url="/cart")
        # 滚动到指定位置-猜你喜欢
        scroll_one_page_until(p, mweb_cart_ele.ele_recommend_tab)

        return p, c, cart_page
    pytestmark = [pytest.mark.h5cart, pytest.mark.todo, pytest.mark.transaction, pytest.mark.suqin]
    @allure.title("【109167-1】 购物车-金额小于$35时换购模块验证")
    def test_109167_mWeb_cart_trade_in_less_35_ui_ux(self, phone_page: dict, h5_autotest_header,
                                                     h5_open_and_close_trace):
        """
        【109167-1】 购物车-金额小于$35时换购模块验证
        测试步骤：
        1. 清空购物车
        2. 从推荐模块加购商品，确保金额小于$35
        3. 验证不显示购物车换购模块
        """
        p, c, cart_page = self.setup_cart_page(phone_page, h5_autotest_header)

        # 获取猜你喜欢商品
        recommend_card = cart_page.FE.eles(mweb_cart_ele.ele_recommend_module_card)

        # 加购一个商品
        if len(recommend_card) > 0:
            recommend_card[0].query_selector(u"//div[@data-testid='btn-atc-plus']").click()
            p.wait_for_timeout(3000)

            # 回到购物车第一个商品位置
            p.query_selector(f"{mweb_cart_ele.ele_cart_normal_card}" + "[1]").scroll_into_view_if_needed()
            p.wait_for_timeout(2000)

            # 获取购物车金额
            total_fee = cart_page.FE.ele(mweb_cart_ele.ele_cart_normal_total + u"//span[2]").text_content().split("$")[
                1]
            log.info(f"当前购物车金额: ${total_fee}")

            # 如果金额大于等于$35，移除商品
            while float(total_fee) >= 35:
                # 移除商品
                cart_page.FE.ele(mweb_cart_ele.ele_cart_normal_card + u"//div[@data-testid='btn-remove']").click()
                p.wait_for_timeout(2000)

                # 如果购物车为空，重新加购
                if not cart_page.FE.ele(mweb_cart_ele.ele_cart_normal_card).is_visible():
                    recommend_card[0].query_selector(u"//div[@data-testid='btn-atc-plus']").click()
                    p.wait_for_timeout(3000)
                    p.query_selector(f"{mweb_cart_ele.ele_cart_normal_card}" + "[1]").scroll_into_view_if_needed()
                    p.wait_for_timeout(2000)

                # 重新获取金额
                total_fee = \
                cart_page.FE.ele(mweb_cart_ele.ele_cart_normal_total + u"//span[2]").text_content().split("$")[1]
                log.info(f"调整后购物车金额: ${total_fee}")

            # 断言金额小于$35
            assert float(total_fee) < 35, f"购物车金额应小于$35，实际为${total_fee}"

            # 断言不显示购物车换购模块
            assert not cart_page.FE.ele(mweb_cart_ele.ele_cart_trade_in).is_visible(), "金额小于$35时不应显示换购模块"
            log.info("验证金额小于$35时不显示换购模块成功")
    pytestmark = [pytest.mark.h5cart, pytest.mark.todo, pytest.mark.transaction, pytest.mark.suqin]
    @allure.title("【109167-2】 购物车-金额大于$35小于$68时换购模块验证")
    def test_109167_mWeb_cart_trade_in_35_to_68_ui_ux(self, phone_page: dict, h5_autotest_header,
                                                      h5_open_and_close_trace):
        """
        【109167-2】 购物车-金额大于$35小于$68时换购模块验证
        测试步骤：
        1. 清空购物车
        2. 从推荐模块加购商品，确保金额大于$35小于$68
        3. 验证显示购物车换购模块
        4. 验证换购文案提示包含"to unlock extra deals!"
        5. 验证换购卡片上不可加购
        6. 点击换购卡片进入换购页面
        """
        p, c, cart_page = self.setup_cart_page(phone_page, h5_autotest_header)

        # 获取猜你喜欢商品
        recommend_card = cart_page.FE.eles(mweb_cart_ele.ele_recommend_module_card)
        cart_amount = 0

        # 加购商品直到金额在$35-$68之间
        for index, item in enumerate(recommend_card):
            if index >= 20:  # 限制循环次数
                break

            # 加购推荐商品
            item.query_selector(u"//div[@data-testid='btn-atc-plus']").click()
            p.wait_for_timeout(3000)

            # 回到购物车第一个商品位置
            p.query_selector(f"{mweb_cart_ele.ele_cart_normal_card}" + "[1]").scroll_into_view_if_needed()
            p.wait_for_timeout(2000)

            # 获取购物车金额
            total_fee = cart_page.FE.ele(mweb_cart_ele.ele_cart_normal_total + u"//span[2]").text_content().split("$")[
                1]
            cart_amount = float(total_fee)
            log.info(f"当前购物车金额: ${cart_amount}")

            # 如果金额在$35-$68之间，跳出循环
            if 35 <= cart_amount < 68:
                break

            # 如果金额大于等于$68，移除一些商品
            if cart_amount >= 68:
                while cart_amount >= 68:
                    # 移除商品
                    cart_page.FE.ele(mweb_cart_ele.ele_cart_normal_card + u"//div[@data-testid='btn-remove']").click()
                    p.wait_for_timeout(2000)

                    # 重新获取金额
                    total_fee = \
                    cart_page.FE.ele(mweb_cart_ele.ele_cart_normal_total + u"//span[2]").text_content().split("$")[1]
                    cart_amount = float(total_fee)
                    log.info(f"调整后购物车金额: ${cart_amount}")

                # 如果金额在$35-$68之间，跳出循环
                if 35 <= cart_amount < 68:
                    break

        # 断言金额在$35-$68之间
        assert 35 <= cart_amount < 68, f"购物车金额应在$35-$68之间，实际为${cart_amount}"

        # 断言换购模块存在
        ele_cart_trade_in = cart_page.FE.ele(mweb_cart_ele.ele_cart_trade_in)
        assert ele_cart_trade_in.is_visible(), "金额在$35-$68之间时应显示换购模块"

        # 断言换购文案提示包含"to unlock extra deals!"
        unlock_deals_text = ele_cart_trade_in.query_selector('text="to unlock extra deals!"')
        assert unlock_deals_text, "换购文案应包含'to unlock extra deals!'"

        # 更精确地匹配整个句子
        full_text = ele_cart_trade_in.query_selector('text=/Add .+ to unlock extra deals!/')
        assert full_text, "换购文案应匹配'Add .+ to unlock extra deals!'"

        # 获取实际文本并验证
        actual_text = full_text.inner_text()
        assert "to unlock extra deals!" in actual_text, f"换购文案应包含'to unlock extra deals!'，实际为'{actual_text}'"

        # 断言换购卡片上不可加购
        assert cart_page.FE.ele(
            mweb_cart_ele.ele_cart_trade_in_card + u"//div[contains(@class,'product-card-btn-shadow')]").is_visible(), "金额在$35-$68之间时换购卡片应不可加购"

        # 点击查看更多，跳转换购页
        cart_page.FE.ele(mweb_cart_ele.ele_cart_trade_in_button).click()
        p.wait_for_timeout(3000)

        # 验证成功跳转到换购页
        assert "/trade-in" in p.url, "应成功跳转到换购页"
        log.info("验证金额在$35-$68之间时换购模块功能成功")
    pytestmark = [pytest.mark.h5cart, pytest.mark.todo, pytest.mark.transaction, pytest.mark.suqin]
    @allure.title("【109167-3】 购物车-金额大于$68时换购模块验证")
    def test_109167_mWeb_cart_trade_in_more_68_ui_ux(self, phone_page: dict, h5_autotest_header,
                                                     h5_open_and_close_trace):
        """
        【109167-3】 购物车-金额大于$68时换购模块验证
        测试步骤：
        1. 清空购物车
        2. 从推荐模块加购商品，确保金额大于$68
        3. 验证显示购物车换购模块
        4. 验证换购卡片上可加购
        5. 点击加购换购商品
        6. 验证换购商品加入购物车成功
        """
        p, c, cart_page = self.setup_cart_page(phone_page, h5_autotest_header)

        # 获取猜你喜欢商品
        recommend_card = cart_page.FE.eles(mweb_cart_ele.ele_recommend_module_card)
        cart_amount = 0

        # 加购商品直到金额大于$68
        for index, item in enumerate(recommend_card):
            if index >= 20:  # 限制循环次数
                break

            # 加购推荐商品
            item.query_selector(u"//div[@data-testid='btn-atc-plus']").click()
            p.wait_for_timeout(3000)

            # 回到购物车第一个商品位置
            p.query_selector(f"{mweb_cart_ele.ele_cart_normal_card}" + "[1]").scroll_into_view_if_needed()
            p.wait_for_timeout(2000)

            # 获取购物车金额
            total_fee = cart_page.FE.ele(mweb_cart_ele.ele_cart_normal_total + u"//span[2]").text_content().split("$")[
                1]
            cart_amount = float(total_fee)
            log.info(f"当前购物车金额: ${cart_amount}")

            # 如果金额大于$68，跳出循环
            if cart_amount >= 68:
                break

        # 断言金额大于等于$68
        assert cart_amount >= 68, f"购物车金额应大于等于$68，实际为${cart_amount}"

        # 断言换购模块存在
        assert cart_page.FE.ele(mweb_cart_ele.ele_cart_trade_in).is_visible(), "金额大于$68时应显示换购模块"

        # 断言换购卡片上可加购
        assert cart_page.FE.ele(mweb_cart_ele.ele_cart_trade_in_card_add).is_visible(), "金额大于$68时换购卡片应可加购"

        # 记录当前购物车商品数量
        cart_items_before = len(cart_page.FE.eles(mweb_cart_ele.ele_cart_normal_card))

        # 点击加购换购商品
        cart_page.FE.ele(mweb_cart_ele.ele_cart_trade_in_card_add).click()
        p.wait_for_timeout(3000)

        # 断言换购商品加入购物车成功
        cart_items_after = len(cart_page.FE.eles(mweb_cart_ele.ele_cart_normal_card))
        assert cart_items_after > cart_items_before, "换购商品应成功加入购物车"
        log.info(f"换购商品成功加入购物车，购物车商品数量从{cart_items_before}增加到{cart_items_after}")

        log.info("验证金额大于$68时换购模块功能成功")