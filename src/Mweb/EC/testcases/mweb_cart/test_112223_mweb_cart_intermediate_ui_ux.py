"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   test_112223_mweb_cart_intermediate_ui_ux.py
@Description    :  购物车-中间-验证中间页的交互
@CreateTime     :  2025/4/25 10:30
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/4/25 10:30
"""
import allure
import pytest
from playwright.sync_api import Page
from src.Mweb.EC.mweb_ele.mweb_cart import mweb_cart_ele
from src.Mweb.EC.mweb_ele.mweb_category import mweb_category_ele
from src.Mweb.EC.mweb_pages.mweb_page_cart.mweb_page_cart import MWebCartPage
from src.Mweb.EC.mweb_pages.mweb_page_category.mweb_page_category import MWebCategorypage
from src.common.commfunc import empty_cart, CommonCheck
from src.config.weee.log_help import log
from src.api.zipcode import switch_zipcode



@allure.story("购物车-中间-验证中间页的交互")
class TestMwebCartIntermediatePage:
    pytestmark = [pytest.mark.mweb_regression, pytest.mark.zhuli]


    @allure.title("[112223] 购物车-中间-验证中间页的交互")
    @pytest.mark.present
    def test_112223_mweb_cart_intermediate_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【112223】 购物车-中间-验证中间页的交互
        测试步骤：
        1. 清空购物车
        2. 从首页进入Deals分类，添加多种类型的商品到购物车（至少3种不同类型）
           - 通过分类筛选加购本地配送商品
           - 通过分类筛选加购Global+商品
           - 通过分类筛选加购Pantry商品
        3. 点击结算，验证中间页面交互
        4. 清空购物车，添加单一类型商品，验证直接跳转结算页
        5. 再次添加多种类型商品，验证勾选一个购物车后的结算流程
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")

        # 1. 切换zipcode到98011
        with allure.step("切换zipcode到98011"):
            try:
                # 如果zipcode不是98011， 则切换到98011
                if not p.locator("//span[text()='98011']").all():
                    switch_zipcode(h5_autotest_header, "98011")
                    p.reload()
                    p.wait_for_timeout(3000)
                    log.info("成功切换zipcode到98011")
            except Exception as e:
                log.warning(f"切换zipcode失败，继续测试: {str(e)}")

        #2.清除购物车
        empty_cart(h5_autotest_header)
        # 3. 进入Sales分类页面
        with allure.step("进入Sales分类页面"):
            # 创建分类页面对象
            category_page = MWebCategorypage(p, h5_autotest_header, browser_context=c, page_url="/category/sale")
            category_page.page.wait_for_timeout(3000)
            log.info("成功进入Sales分类页面")

        # 4. 筛选Local Delivery商品并加购2个
        with allure.step("筛选Local Delivery商品并加购2个"):
            # 使用封装方法筛选并加购Local Delivery商品
            local_added = category_page.add_products_with_filter("Local Delivery",
                                                                 mweb_category_ele.ele_local_delivery_xpath, 2)

            # 验证加购结果
            assert local_added > 0, "未能成功加购Local Delivery商品"
            log.info(f"成功加购{local_added}个Local Delivery商品")

        # 5. 重置筛选条件
        with allure.step("重置筛选条件"):
            category_page.reset_filter()
            log.info("筛选条件已重置")

            # 等待页面刷新
            p.wait_for_timeout(3000)

        # 6. 筛选Pantry商品并加购2个
        with allure.step("筛选Pantry商品并加购2个"):
            # 使用封装方法筛选并加购Pantry商品
            pantry_added = category_page.add_products_with_filter("Pantry", mweb_category_ele.ele_pantry_delivery_xpath,
                                                                  2)

            # 验证加购结果
            assert pantry_added > 0, "未能成功加购Pantry商品"
            log.info(f"成功加购{pantry_added}个Pantry商品")

        # 7. 进入购物车页面验证商品已添加成功
        with allure.step("进入购物车页面验证商品已添加成功"):
            # 创建购物车页面对象
            cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url="/cart")
            p.wait_for_timeout(3000)

            # 验证购物车中的商品
            cart_items = cart_page.page.query_selector_all("//div[@data-testid='wid-product-card-container']")
            total_items = len(cart_items)

            # 验证总数
            assert total_items >= local_added + pantry_added, f"购物车商品数量不符，期望至少{local_added + pantry_added}个，实际{total_items}个"
            log.info(f"购物车中共有{total_items}个商品，验证成功")
            cart_page.page.wait_for_timeout(2000)
            # 验证购物车中有不同类型的商品
            normal_cart = p.locator(mweb_cart_ele.ele_cart_normal).is_visible(timeout=3000)
            pantry_cart = p.locator(mweb_cart_ele.ele_cart_pantry).is_visible(timeout=3000)

            if local_added > 0 and pantry_added > 0:
                assert normal_cart and pantry_cart, "购物车中未显示所有类型的商品"
            elif local_added > 0:
                assert normal_cart, "购物车中未显示Local Delivery商品"
            elif pantry_added > 0:
                assert pantry_cart, "购物车中未显示Pantry商品"

            log.info("成功验证购物车中的商品")

        # 8. 点击结算，验证中间页面交互
        with allure.step("点击结算，验证中间页面交互"):
            # 调用封装的方法验证中间页交互
            assert cart_page.verify_cart_middle_page_interaction(), "验证中间页交互失败"
            log.info("中间页交互验证通过")
