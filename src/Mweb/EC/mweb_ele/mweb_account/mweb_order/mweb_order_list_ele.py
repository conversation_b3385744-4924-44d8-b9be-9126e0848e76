"""
<AUTHOR>  Assistant
@Version        :  V1.0.0
------------------------------------
@File           :   mweb_order_list_ele.py
@Description    :  Mobile订单列表页面元素定位
@CreateTime     :  2025/1/24
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/1/24
"""

# 订单列表页面标题
order_list_title = ""
order_list_search = "btn-order-list-search"
# 曾经购买
order_list_bought = "wid-order-list-bought-again"

# 空状态页面元素
empty_image_ele = "wid-order-list-empty-image"
empty_title_ele = "wid-order-list-empty-title"
empty_description_ele = ""
start_shopping_btn_ele = "btn-order-list-empty-go-shopping"
browse_btn_ele = ""

# 订单状态Tab元素 (使用data-testid)
order_all_tab_ele = "wid-order-list-tab-all"
order_pending_tab_ele = "wid-order-list-tab-1"
order_unshipped_tab_ele = "wid-order-list-tab-2"
order_shipped_tab_ele = "wid-order-list-tab-3"
order_review_tab_ele = "wid-order-list-tab-6"
order_cancelled_tab_ele = "wid-order-list-tab-4"

# 订单卡片及订单内容
# 生鲜订单
order_list_R_card_ele = "wid-order-list-order-card-R-normal-0"
# mkpl 订单
order_list_S_card_ele = "wid-order-list-order-card-S-normal-0"
# 积分订单
order_list_P_card_ele = "wid-order-list-order-card-V-point-0"
# 礼品卡订单
order_list_G_card_ele = "wid-order-list-order-card-V-giftcard-0"

order_list_card_status_ele =  "wid-order-list-status"
# order button
order_pay_btn = "wid-order-btn-pay"
order_cancel_btn = "wid-order-btn-cancel"
order_track_btn = "wid-order-btn-logistics_tracking"
order_post_btn = "wid-order-btn-post"
order_buy_again_btn = "wid-order-btn-buy_again"
order_view_refund_btn = "wid-order-btn-view_refund"
order_order_share_btn = "wid-order-btn-order_share"
order_back_btn = "wid-page-nav-header-back-button"

# 取消订单pop up
cancel_order_pop = ""
cancel_order_pop_box = ""
cancel_order_pop_tips = ""
cancel_order_pop_close_btn = ""
cancel_order_pop_btn = ""
pending_cancel_order_pop = ""
pending_cancel_order_pop_btn = ""

