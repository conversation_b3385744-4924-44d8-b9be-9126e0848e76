
# ================================
# 商家关注按钮选择器
# ================================

# 关注按钮 - 基于data-testid（最精确）
ele_follow_seller_btn = "btn-follow-seller"

# ================================
# Email输入框选择器
# ================================

# Email输入框 - 基于id（最精确）
ele_email_input = u"//input[@id='email']"

# Email输入框 - 基于type属性
ele_email_input_type = u"//input[@type='email']"

# Email输入框 - 基于autocomplete属性
ele_email_input_autocomplete = u"//input[@autocomplete='email']"

# ================================
# 下一步按钮选择器
# ================================

# 下一步按钮 - 基于文本内容（最精确）
ele_next_step_btn = u"//button[text()='下一步']"


# 下一步按钮 - 基于主要按钮样式
ele_next_step_btn_primary = u"//button[contains(@class, 'bg-btn-primary-bg') and contains(@class, 'text-btn-primary-fg-default') and text()='下一步']"
# ================================
# 密码输入框选择器
# ================================

# 密码输入框 - 基于id（最精确）
ele_password_input = u"//input[@id='password']"

# 密码输入框 - 基于type属性
ele_password_input_type = u"//input[@type='password']"

# 密码输入框 - 基于autocomplete属性
ele_password_input_autocomplete = u"//input[@autocomplete='password']"

# 密码输入框 - 基于CSS类组合
ele_password_input_css = u"//input[contains(@class, 'w-full') and contains(@class, 'h-full') and @type='password']"

# 密码输入框 - 组合选择器（最可靠）
ele_password_input_combined = u"//input[@type='password' and @id='password' and @autocomplete='password']"
# ================================
# 确定按钮选择器
# ================================

# 确定按钮 - 基于data-testid（最精确）
ele_confirm_btn = "btn-unfollow-seller"


# ================================
# 取消按钮选择器
# ================================

# 取消按钮 - 基于data-testid（最精确）
ele_cancel_btn = "btn-cancel-unfollow-seller"


# 取消关注确认弹窗选择器
# ================================

# 取消关注弹窗 - 基于data-testid（最精确）
ele_unfollow_modal = "mod-unfollow-confirm-modal"

# 弹窗关闭按钮 - 基于data-testid
ele_close_unfollow_popup_btn = "btn-colose-unfollow-popup"


# ================================
# 弹窗容器选择器（移动端）
# ================================

# 弹窗容器 - 基于data-testid（最精确）
ele_unfollow_modal_container = "mod-unfollow-confirm-modal"

# 弹窗关闭按钮 - 基于data-testid
ele_unfollow_close_btn = "btn-colose-unfollow-popup"

# 弹窗确定按钮 - 基于data-testid
ele_unfollow_confirm_btn = "btn-unfollow-seller"

# 弹窗取消按钮 - 基于data-testid
ele_unfollow_cancel_btn = "btn-cancel-unfollow-seller"

# ================================
# 商家标题选择器
# ================================

# 商家标题 - 基于data-testid（最精确）
ele_seller_title = "wid-seller-title"

ele_seller_logo = "wid-seller-logo"

# ================================
# 商家标签页选择器
# ================================

# 标签页容器 - 基于data-testid（最精确）
ele_seller_tabs_container = "wid-seller-tabs-container"

# 探索标签页 - 基于data-testid（最精确）
ele_seller_tab_explore = u"//div[@data-testid='&quot;wid-seller-tab-explore']"


# 全部商品标签页 - 基于data-testid（最精确）
ele_seller_tab_all = u"//div[@data-testid='\"wid-seller-tab-all']"

# 全部商品标签页 - 备用选择器
ele_seller_tab_all_backup = u"//div[contains(@data-testid, 'wid-seller-tab-all')]"

# 晒单标签页 - 基于data-testid（最精确）
ele_seller_tab_reviews = u"//div[@data-testid='&quot;wid-seller-tab-reviews']"


# 一键置顶按钮定义：
ele_seller_back_to_top = "btn-back-to-top"


# ================================
# 搜索按钮选择器
# ================================

# 搜索按钮 - 基于data-testid（最精确）
ele_search_btn = "btn-open-search"

# 商家星级评价：
ele_seller_rating = "wid-seller-rating"

# 商家ETA 信息：
ele_seller_estimate_range = "wid-seller-estimate-range"

# 商家发货及运费信息
ele_seller_reminder_shipping = "wid-seller-reminder-shipping"

# 商家message 入口
ele_send_message_to_seller = "btn-send-message-to-seller"

# 分享商家message 入口
ele_share_seller_info = "btn-share-seller-info"

# ================================
# 访问商家详情按钮选择器
# ================================

# 访问商家详情按钮 - 基于data-testid
ele_visit_seller_detail = "btn-visit-seller-detail"

# 商家详情-评价tab
ele_seller_detail_feedback = "wid-seller-tab-feedback"

# 商家详情-运输tab
ele_seller_shipping_return = "wid-seller-tab-shipping_return"

# 商家详情-关于tab
ele_seller_about_tab = "wid-seller-tab-about"

# 商家详情-整体评分tab
ele_seller_rating_info = "wid-feedback-statistics-overall_rating"


