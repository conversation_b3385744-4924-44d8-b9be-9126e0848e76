"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   mweb_checkout_ele.py
@Description    :  结算页面元素定位
@CreateTime     :  2025/5/16 15:04
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/5/16 15:30
"""

# 页面标题
ele_checkout_title = u"//div[text()='Checkout']"

# 返回按钮
ele_back_button = u"//div[@data-testid='btn-back']"

# 页面头部
ele_header = u"//div[@id='layout-header']"
ele_header_title = u"//div[@data-role='middle']//div[contains(@class, 'text-primary-1')]"

# 配送信息模块
ele_delivery_info_section = u"//div[@data-testid='mod-checkout-delivery-info']"
ele_delivery_info_title = u"//div[@data-testid='mod-checkout-delivery-info']//div[contains(@class, 'enki-body-base-medium')]"
ele_delivery_address = u"//div[@data-testid='mod-checkout-delivery-info']//div[contains(@class, 'p-5 border')]"
ele_delivery_address_name = u"//div[@data-testid='mod-checkout-delivery-info']//div[contains(@class, 'enki-body-sm-strong')]//p"
ele_delivery_address_detail = u"//div[@data-testid='mod-checkout-delivery-info']//div[contains(@class, 'enki-body-sm')]//div"

# 支付方式模块
ele_payment_method_section = u"//div[@id='payment']"
ele_payment_method_title = u"//div[@id='payment']//div[contains(@class, 'enki-body-base-medium')]"
ele_payment_method_card = u"//div[@id='payment']//div[contains(@class, 'p-5 border')]"
ele_payment_card_number = u"//div[@id='payment']//div[contains(@class, 'enki-body-sm-strong')]"

# 优惠券模块
ele_coupon_section = u"//div[@id='coupon']"
ele_coupon_title = u"//div[@id='coupon']//div[contains(@class, 'enki-body-base-medium')]"
ele_coupon_select_box = u"//div[@id='coupon']//div[contains(@class, 'p-5 border')]"
ele_coupon_value = u"//div[@id='coupon']//div[contains(@class, 'enki-body-sm-strong')]"

# 订单摘要模块
ele_order_summary_section = u"//div[@id='summary']"
ele_order_summary_title = u"//div[@id='summary']//div[contains(@class, 'enki-body-base-medium')]"
ele_order_subtotal = u"//div[contains(text(), 'Subtotal')]/following-sibling::div"
ele_order_delivery_fee = u"//div[contains(text(), 'Delivery')]/following-sibling::div"
ele_order_tax = u"//div[contains(text(), 'Tax')]/following-sibling::div"
ele_order_total = u"//div[contains(text(), 'Total')]/following-sibling::div"

# 结算按钮
ele_place_order_button = 'btn-place-order'
ele_place_order_button_text = u"//button[@data-testid='btn-place-order']/span"

# 底部提示信息
ele_bottom_tip = u"//div[contains(@class, 'text-surface-100-fg-minor') and contains(@class, 'text-center')]"

# 错误提示
ele_error_message = u"//div[contains(@class, 'text-error-500')]"

# 加载状态
ele_loading_indicator = u"//div[contains(@class, 'loading')]"

# 支付方式选择
ele_payment_method_selector = 'payment-method-selector'
ele_credit_card_option = 'payment-method-credit-card'
ele_paypal_option = 'payment-method-paypal'

# 配送时间选择
ele_delivery_time_section ='delivery-time-selector'
ele_delivery_time_option = 'delivery-time-option'

# 商品列表
ele_product_list ='checkout-product-list'
ele_product_item = 'checkout-product-item'
ele_product_name = 'checkout-product-name'
ele_product_price = 'checkout-product-price'
ele_product_quantity = 'checkout-product-quantity'

# 提示横幅
ele_promo_banner = u"//div[contains(@style, 'background: rgb(253, 255, 164)')]"
ele_promo_banner_text = u"//div[contains(@style, 'background: rgb(253, 255, 164)')]//span"
