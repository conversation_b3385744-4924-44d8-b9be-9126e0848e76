# mini空购物车img

ele_mini_cart_img = u"//div[contains(@class,'MiniCart_emptyCartWrapper')]//img[contains(@src,'cart_empty')]"
# mini空购物车下Your cart is empty文案
ele_mini_cart_text = u"//div[contains(@class,'MiniCart_emptyCartWrapper')]//div[text()='Your cart is empty']"
# mini 购物车
# mini 购物车进度条模块
ele_mini_progress = u"//div[contains(@class,'ProgressTip_progressTip')]"
# mini 购物车进度条文案
ele_mini_progress_tip_copy = u"(//div[contains(@class,'ProgressTip_progressTip')]//div)[1]//div"
# mini 购物车进度条
ele_mini_progress_tip_bar = ele_mini_progress + u"//div[contains(@class,'ProgressTip_bar')]"

ele_mini_items_num = u"//div[contains(@class,'MiniCart_panelHeader')]//span[@class='text-surface-100-fg-default']"
ele_mini_items_list = u"//div[contains(@class,'MiniCart_goodsCart')]"
ele_mini_goto_cart_button = u"//div[contains(@class,'MiniCart_footer')]//button"
# ###############################空购物车 模块#########################################
# 空购物车img
ele_cart_img = u"//img[contains(@src,'cart_empty')]"
# 空购物车下Your cart is empty文案
ele_cart_text = u"//main[@id='cart-main']//div[text()='Your cart is empty']"
# 空购物车Start shopping按钮
ele_cart_start_shopping = u"//div[contains(@class,'cart-v2_emptyBtnWrapper')]//button[@shape='round']"

# ###############################购物车模块 模块#########################################
# 生鲜/pantry/seller购物车
ele_cart_normal = u"//div[contains(@data-testid,'wid-cart-summary-normal')]"
ele_cart_pantry = u"//div[contains(@data-testid,'wid-cart-summary-pantry')]"
ele_cart_seller = u"//div[contains(@data-testid,'wid-cart-summary-seller')]"

# ###############################购物车免运费banner模块#########################################
# 元素待补充
ele_cart_banner_normal = ele_cart_normal+ u"//div[contains(@class,'SelectableCart_cartPanel')]//*[contains(text(), 'free delivery')]"
ele_cart_banner_normal_card = ele_cart_banner_normal
ele_cart_banner_pantry = ele_cart_pantry+u"//div[contains(@class,'SelectableCart_cartPanel')]//*[contains(text(), 'free delivery')]"
ele_cart_banner_pantry_card = ele_cart_banner_pantry


# ###############################购物车换购模块模块#########################################
# 元素待补充
ele_cart_trade_in_normal = u"//div[contains(@class,'CartActivity_activityWrapper')]"
ele_cart_trade_in_normal_card = ele_cart_trade_in_normal
ele_cart_trade_in_pantry = u"//div[contains(@class,'CartActivity_activityWrapper')]"
ele_cart_trade_in_pantry_card = ele_cart_trade_in_normal

# ###############################购物车活动模块 模块#########################################
# 元素待补充
ele_cart_activity_normal = ele_cart_normal+ u"//div[contains(@class,'CartActivity_activityWrapper')]"
ele_cart_activity_pantry = ele_cart_pantry+ u"//div[contains(@class,'CartActivity_activityWrapper')]"

ele_cart_activity_normal_card = ele_cart_activity_normal+ u"//div[contains(@class,'CartActivity_activityWrapper')]"
ele_cart_activity_pantry_card = ele_cart_activity_pantry+ u"//div[contains(@class,'CartActivity_activityWrapper')]"


# 生鲜购物车切换日期按钮  -- PC 这里没有id 找不到
ele_cart_normal_delivery_date = ele_cart_normal + u"//button[@data-testid='btn-change-delivery-date']"

# 购物车商品卡片
ele_cart_normal_card = ele_cart_normal+u"//div[@data-testid='wid-cart-section-goods']"
ele_cart_pantry_card = ele_cart_pantry+u"//div[@data-testid='wid-cart-section-goods']"
ele_cart_seller_card = ele_cart_seller+u"//div[@data-testid='wid-cart-section-goods']"
# save for later 按钮
ele_cart_normal_s4l = ele_cart_normal+u"//div[@data-testid='wid-cart-section-save-for-later']"
ele_cart_pantry_s4l = ele_cart_pantry+u"//div[@data-testid='wid-cart-section-save-for-later']"
ele_cart_seller_s4l = ele_cart_seller+u"//div[@data-testid='wid-cart-section-save-for-later']"
# remove 按钮
ele_cart_normal_remove = ele_cart_normal_card+u"//div[@data-testid='wid-cart-section-remove']"
ele_cart_pantry_remove = ele_cart_pantry_card+u"//div[@data-testid='wid-cart-section-remove']"
ele_cart_seller_remove = ele_cart_seller_card+u"//div[@data-testid='wid-cart-section-remove']"

# 购物车商品列表
ele_cart_products = u"//div[@data-testid='wid-cart-section-goods']"
ele_remove = u"//div[@data-testid='wid-cart-section-remove']"
ele_save_for_later = u"//div[@data-testid='wid-cart-section-save-for-later']"
ele_cart_product_title = u"//div[contains(@class, 'GoodsInCart_name')]//span"

# ###############################save for later 模块#########################################

#
# ###############################中间页 模块#########################################

# 选择要结算的购物车
ele_cart_select_carts_to_checkout = u"//button[text()='Select carts to checkout']"
# 结算所有购物车
ele_cart_select_all_carts = u"//p[contains(text(), 'Select all carts')]"
# checkout按钮
ele_cart_checkout = u"//button[text()='Checkout']"

# upsell
ele_cart_upsell_continue_checkout = u"//button[text()='Continue to checkout']"

# 选择地址
ele_cart_select_address = u"(//div[@data-id]//div[contains(text(), 'Bothell')])[1]"
# place order
ele_cart_place_order = u"//span[@class='font-semibold']/following-sibling::button"

# ###############################左侧购物车各种费用金额#########################################
# 生鲜/pantry/seller购物车 各种费用金额模块
ele_cart_normal_cart_info = ele_cart_normal+ u"//div[contains(@class,'SelectableCart_info')]"
ele_cart_pantry_cart_info = ele_cart_pantry+ u"//div[contains(@class,'SelectableCart_info')]"
ele_cart_seller_cart_info = ele_cart_seller+ u"//div[contains(@class,'SelectableCart_info')]"
# 生鲜/pantry/seller购物车 items total金额
ele_cart_normal_item_total = ele_cart_normal_cart_info+ u"//div[contains(@class,'SelectableCart_right')]"+ u"//div[2]//div[1]//span[2]"
ele_cart_pantry_item_total = ele_cart_pantry_cart_info+ u"//div[contains(@class,'SelectableCart_right')]"+ u"//div[2]//div[1]//span[2]"
ele_cart_seller_item_total = ele_cart_seller_cart_info+ u"//div[contains(@class,'SelectableCart_right')]"+ u"//div[2]//div[1]//span[2]"

# 生鲜/pantry/seller购物车 Delivery fee金额
ele_cart_normal_delivery_fee = ele_cart_normal_cart_info+ u"//div[contains(@class,'SelectableCart_right')]"+ u"//div[2]//div[2]//span[2]"
ele_cart_pantry_delivery_fee = ele_cart_pantry_cart_info+ u"//div[contains(@class,'SelectableCart_right')]"+ u"//div[2]//div[2]//span[2]"
ele_cart_seller_delivery_fee = ele_cart_seller_cart_info+ u"//div[contains(@class,'SelectableCart_right')]"+ u"//div[2]//div[2]//span[2]"

# 生鲜/pantry/seller购物车 Service fee金额
ele_cart_normal_service_fee = ele_cart_normal_cart_info+ u"//div[contains(@class,'SelectableCart_right')]"+ u"//div[2]//div[3]//span[2]"
ele_cart_pantry_service_fee = ele_cart_pantry_cart_info+ u"//div[contains(@class,'SelectableCart_right')]"+ u"//div[2]//div[3]//span[2]"
ele_cart_seller_service_fee = ele_cart_seller_cart_info+ u"//div[contains(@class,'SelectableCart_right')]"+ u"//div[2]//div[3]//span[2]"
# ###############################右侧summary购物车列表模块#########################################
# 如果为多个购物车，则返回list
ele_cart_summary_list = u"//main[@id='cart-main']//div[contains(@data-testid,'wid-cart-summary')]"
# 右侧购物车summary模块
ele_cart_summary_total_list = u"//div[@data-testid='wid-cart-summary-main']"

# 右侧summary 文案
# ele_cart_summary = u"//div[text()='Summary']"
ele_cart_summary = ele_cart_summary_total_list + u"//div[contains(@class,'PanelCheckout_title')]"

# 右侧 summary下面第一个购物车为local delivery 文案
ele_cart_summary_local_delivery = ele_cart_summary_total_list + u"//div[@data-testid='wid-cart-summary-item-0']//div[1]//span[contains(@class, 'items-center')]"
# 右侧 summary下面items_total
ele_cart_items_total = u"//div[@class='transition-all']//span[text()='Items total']/following-sibling::span"

# ele_cart_items_total = u"//div[@class='relative']//span[text()='Items total']/following-sibling::span"
# 如果为多个购物车，则返回list
ele_cart_delivery_fee = u"//div[@class='relative']//span[text()='Delivery fee']/following-sibling::span"
# subtotal
ele_cart_subtotal = u"//div[@data-testid='wid-cart-summary-subtotal']"

# ele_cart_subtotal = u"//span[text()='Subtotal']"
# subtotal的金额
ele_cart_subtotal_fee = ele_cart_subtotal + u"//following-sibling::span"
# ele_cart_subtotal_fee = u"//span[text()='Subtotal']/following-sibling::span"
# 购物车标题 css selector, 有多个购物车返回列表
ele_cart_each_cart_div = u"//main[@id='cart-main']//div[contains(@class, 'SelectableCart_combineItem')]"
ele_cart_each_title = u"#cart-main div[class^='SelectableCart_combineItem'] h2"
# 每个购物车的商品， 在程序中处理
# ################################猜你喜欢模块######################################
# 猜你喜欢模块
# 购物车中的recommendations
ele_cart_recommendations = u"//span[text()='Recommendations']"
# recommendations下面的所有商品，list
ele_cart_recommendations_all_goods = ele_cart_recommendations + u"/../following-sibling::div//div[contains(@class, 'List_item')]"
# 为你推荐模块
ele_recommend_module = u"//div[contains(@class,'cart-v2_recommends')]"
# 再次购买模块
ele_buy_again_module = u"//div[contains(@class,'cart-v2_histories')]"
# 为你推荐模块商品卡片
ele_recommend_module_card = ele_recommend_module + u"//div[contains(@class,'List_item')]"
# 再次购买模块商品卡片
ele_buy_again_module_card = ele_buy_again_module + u"//div[contains(@class,'List_item')]"
