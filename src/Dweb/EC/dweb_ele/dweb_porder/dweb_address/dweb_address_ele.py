# PC端地址相关元素定位
# 首页地址按钮
ele_home_address_button = u"//div[@id='changeZipCode']"
ele_home_zipcode = "wid-modal-zip-code"
# ########################################################################
# 首页修改您的地址pop元素
# zipcode 弹窗确认按钮
zipcode_pop_btn = "wid-btn-zipcode-btn"
# 地址卡片
address_card = "wid-address-card"
address_card_location_icon = "wid-address-card-location-icon"
address_card_name = "wid-address-card-name"
address_card_address = "wid-address-card-address"
address_card_city = "wid-address-card-city"
address_card_edit = "wid-address-card-edit"

# 查看更多按钮
zipcode_pop_more_btn = "wid-btn-more-address"
# 添加地址按钮
zipcode_pop_add_btn = "wid-btn-add-address"
# zipcode 弹窗x按钮
zipcode_pop_close_btn  = "wid-modal-btn-close"
# 新增地址按钮
ele_add_new_address_button = u"//button[text()='Add new address']"
# ########################################################################
# 新增地址表单元素
address_first_matched = u"#streetAddressList span"
# 输入姓名
address_first_name = "wid-input-first-name"
address_last_name = "wid-input-last-name"
# 输入电话
address_phone = "wid-input-phone"
# 输入街道地址
address_street = "wid-input-street"
# 输入公寓号
address_apt = "wid-input-flats"
# 输入城市
address_city = u"input[placeholder='City']"
# 输入州
address_state = "text-filed-input"
# 输入邮编
address_zipcode = "wid-input-zipcode"
# 输入备注
address_note = "wid-input-notes"
# 保存地址
address_save_button = "btn-save-address"
# 取消保存
address_cancel_button = u"button[type='button']"
# 删除按钮
address_delete_button = "btn-delete-address"

# 地址簿中的地址
ele_address_book_item = u"//div[contains(@class, 'AddressCard_addressCard')]"
ele_address_book_name = u"//div[contains(@class, 'AddressCard_addressCard')]//strong"

# 账户页面元素
ele_account_button = u"//div[@data-testid='wid-account-menu']"
ele_account_addresses = u"//a[contains(@href, '/account/addresses')]"

# 订单详情页元素
ele_order_detail_address_section = u"//div[contains(@class, 'OrderDetail_addressSection')]"
ele_order_detail_change_address = u"//span[text()='Change']"

# Checkout页面元素
ele_checkout_address_section = u"//div[@data-testid='wid-checkout-address-selector']"
ele_checkout_add_address = u"//button[text()='Add new address']"

# 删除地址
ele_address_delete_button = u"//i[contains(@class, 'iconDelete')]"
ele_address_confirm_delete = u"//button[text()='Remove']"

# 测试用地址名称
ele_test_address_name = u"//strong[text()='Test Automation']"
