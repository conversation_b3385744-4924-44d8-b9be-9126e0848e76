"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   mweb_order_confirmation_ele.py
@Description    :  订单确认页面元素定义
@CreateTime     :  2025/6/8 11:31
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/6/8 11:31
"""
# 订单成功页弹窗元素
dialog = u"//div[@id='radix-«r0»']"

# 弹窗关闭按钮元素

dialog_close = u"//button[@data-testid='wid-modal-btn-close']"

# 开始赚取积分按钮
start_earning = u"//div[contains(@class, 'text-white')]"

# 订单确认页面主容器
ele_order_confirmation_container = u"//div[@id='order-confirmation-container']"

# 订单确认页面标题
ele_order_confirmation_title = u"//p[contains(text(), 'Order confirmed')]"

# 订单号信息
ele_order_number = u"//div[contains(text(), 'Order #')]"


# 订单成功页操作按钮
ele_order_details = u"//span[contains(text(), 'Order details')]"
ele_continue_shopping = u"//span[contains(text(), 'Continue shopping')]"

# 分享订单按钮
ele_share_order = u"//button[contains(text(), 'Share')]"

# 关闭弹窗按钮
ele_modal_close = u"//button[@data-testid='wid-modal-btn-close']"


# 订单确认消息
ele_confirmation_message = u"//div[contains(text(), 'Thank you for your order')]"
ele_confirmation_car = u"//div[@class='w-[330px]']"
ele_confirmation_delivery_window= u"//div[@class='mr-5']"
ele_confirmation_Delivery_address= u"//div[@class='mt-4']"

# 推荐商品区域
ele_recommendations = u"//div[contains(text(), 'Recommended for you')]"
ele_recommendation_item = u"//div[contains(@class, 'recommendation-item')]"
ele_recommendation_add_to_cart = u"//button[contains(text(), 'Add to cart')]"
