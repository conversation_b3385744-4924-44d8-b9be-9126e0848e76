"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   dweb_order_confirmation_ele.py
@Description    :  订单确认页面元素定义
@CreateTime     :  2025/6/8 11:31
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/6/8 11:31
"""
# 订单成功页弹窗元素
dialog = u"//div[@role='dialog' and contains(@class, 'fixed')]"

# 弹窗关闭按钮元素

dialog_close = u"//button[@data-testid='wid-modal-btn-close']"

# 开始赚取积分按钮
start_earning = u"//div[contains(@class, 'text-white')]"

# 订单确认页面主容器
ele_order_confirmation_container = u"//div[@id='order-confirmation-container']"

# 订单确认页面标题
ele_order_confirmation_title = u"//p[contains(text(), 'Order confirmed')]"

# 订单号信息
ele_order_number = u"//div[contains(text(), 'Order #')]"

# 配送信息区域
ele_delivery_info = u"//div[contains(text(), 'Delivery Information')]"
ele_delivery_date = u"//div[contains(text(), 'Estimated Delivery')]"
ele_delivery_address = u"//div[contains(text(), 'Delivery Address')]"

# 支付信息区域
ele_payment_info = u"//div[contains(text(), 'Payment Information')]"
ele_payment_method = u"//div[contains(text(), 'Payment Method')]"

# 订单商品列表
ele_order_items = u"//div[contains(@class, 'order-items')]"
ele_order_item = u"//div[contains(@class, 'order-item')]"

# 订单摘要区域
ele_order_summary = u"//div[contains(text(), 'Order Summary')]"
ele_subtotal = u"//div[contains(text(), 'Subtotal')]"
ele_delivery_fee = u"//div[contains(text(), 'Delivery Fee')]"
ele_tax = u"//div[contains(text(), 'Tax')]"
ele_total = u"//div[contains(text(), 'Total')]"

# 操作按钮
ele_view_order_details = u"//button[contains(text(), 'View Order Details')]"
ele_continue_shopping = u"//button[contains(text(), 'Continue Shopping')]"
ele_track_order = u"//button[contains(text(), 'Track Order')]"

# 分享订单按钮
ele_share_order = u"//button[contains(text(), 'Share')]"

# 关闭弹窗按钮
ele_modal_close = u"//button[@data-testid='wid-modal-btn-close']"

# 使用data-testid的元素
ele_modal_btn_close = u"//button[@data-testid='wid-modal-btn-close']"

# 从HTML片段中提取的元素
ele_weee_logo = u"//div[@role='heading']"
ele_data_layer = u"//script[@id='weee_dataLayer_defined']"

# 订单确认消息
ele_confirmation_message = u"//div[contains(text(), 'Thank you for your order')]"

# 推荐商品区域
ele_recommendations = u"//div[contains(text(), 'Recommended for you')]"
ele_recommendation_item = u"//div[contains(@class, 'recommendation-item')]"
ele_recommendation_add_to_cart = u"//button[contains(text(), 'Add to cart')]"
