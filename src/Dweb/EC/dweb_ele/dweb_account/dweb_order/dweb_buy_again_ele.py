buy_again_reorder_header_ele = u"//div[contains(@class,'buy-again_reorderHeader')]"
buy_again_date_info_ele = u"//div[contains(@class,'buy-again_dateInfo')]"
buy_again_select_all_ele = u"//div[contains(@class,'buy-again_selectAll')]"

# buy_again_select_all_ele = u"//div[contains(@class,'buy-again_selectAll') and contains(@class, 'buy-again_selected')]"
buy_again_unselect_all_ele = u"//div[contains(@class,'buy-again_selectAll') and contains(@class, 'buy-again_disabledSelect')]"
buy_again_list_ele = u"//div[contains(@class,'buy-again_list')]"
buy_again_product_card_ele = u"//div[contains(@class,'buy-again_productCard')]"
buy_again_selected_ele = u"//div[contains(@class, 'buy-again_productCard') and contains(@class, 'buy-again_selected')]"
buy_again_invalid_ele = u"//div[contains(@class, 'buy-again_productCard') and contains(@class, 'buy-again_inavailable')]"
buy_again_add_cart_button_ele = u"//button[contains(@class,'buy-again_addCart')]"

#  以上元素是老pc的，需要重新写
buy_again_x_btn = "//button[@data-testid='wid-drawer-close']"

# 再来一单页面
buy_again_page_close = "btn-drawer-close"
# 切换日期入口
# H5
buy_again_change_date = "wid-order-buy-again-change-date"
buy_again_change_date_label ="wid-order-buy-again-change-date-label"
buy_again_change_date_value ="wid-order-buy-again-change-date-value"

# PC
buy_again_page_info = "wid-account-buy-again-date-info"
buy_again_page_title = "wid-account-buy-again-title"
buy_again_page = "wid-order-buy-again-content"
buy_again_available = "wid-order-buy-again-content-available"
# 再来一单页面-商品列表
# H5
buy_again_available_product = "wid-order-buy-again-product-item"
# PC
buy_again_available_item = "wid-account-buy-again-product-item"

buy_again_available_item_content = "wid-order-buy-again-product-item-content"
buy_again_unavailable = "wid-order-buy-again-content-inavailable"
buy_again_item_checkbox = "wid-order-buy-again-product-item-checkbox"
# 再来一单页面-加入购物车按钮
# H5
buy_again_add_cart_button = "wid-order-buy-again-submit"
buy_again_add_cart_button_count = "wid-order-buy-again-submit-count"
# pc
buy_again_add_cart_btn= "btn-account-buy-again-add-cart"
# 再来一单页面-全选按钮
# H5
buy_again_chose_all = "wid-order-buy-again-chose-all"
# pc
buy_again_select_all = "wid-account-buy-again-select-all"

