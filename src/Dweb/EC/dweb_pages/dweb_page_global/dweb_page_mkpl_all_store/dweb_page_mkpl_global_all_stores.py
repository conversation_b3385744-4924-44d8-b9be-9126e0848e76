from playwright.sync_api import Page

from src.Dweb.EC.dweb_ele.dweb_mkpl_global import dweb_mkpl_global_ele
from src.common.commonui import close_advertise_on_home
from src.config.base_config import TEST_URL
from src.Dweb.EC.dweb_pages.dweb_common_page import DWebCommonPage
from src.config.weee.log_help import log


class DWebMkplGlobalComprehensivePage(DWebCommonPage):
    """
    PC web端 全球购商店列表页面的测试操作
    """
    def __init__(self, page: Page, header, browser_context, page_url: str = None):
        """
        1. 进入全球购商店页面
        2. 调用close_advertise_on_home(self.page)关掉首页的广告
        """
        super().__init__(page, header)
        self.bc = browser_context
        
        # 设置默认页面URL
        if page_url is None:
            page_url = "/mkpl/global?mode=sub_page&hide_activity_pop=1"
        
        # 直接进入全球购页面
        self.page.goto(TEST_URL + page_url)
        log.info(f"已访问全球购页面: {TEST_URL + page_url}")

        close_advertise_on_home(self.page)
        log.info("页面初始化完成")

    def wait_for_page_load(self, timeout_seconds=5):
        """
        等待页面加载完成
        """
        log.info(f"等待页面加载完成 {timeout_seconds} 秒")
        self.page.wait_for_timeout(timeout_seconds * 1000)
        log.info("页面加载等待完成")

    def click_japan_tab(self):
        """
        点击Japan tab
        """
        log.info("开始点击Japan tab")
        
        # 使用get_by_test_id定位元素
        japan_element = self.page.get_by_test_id(dweb_mkpl_global_ele.ele_japan_store)
        if japan_element.count() == 0:
            log.warning("未找到Japan tab元素")
            return False
        
        # 滚动到元素位置
        if not japan_element.is_visible():
            japan_element.scroll_into_view_if_needed()
            self.page.wait_for_timeout(1000)
        
        # 点击Japan tab
        japan_element.click()
        log.info("已点击Japan tab")
        
        return True

    def click_korea_tab(self):
        """
        点击Korea tab
        """
        log.info("开始点击Korea tab")
        
        # 使用get_by_test_id定位元素
        korea_element = self.page.get_by_test_id(dweb_mkpl_global_ele.ele_korea_store)
        if korea_element.count() == 0:
            log.warning("未找到Korea tab元素")
            return False
        
        # 滚动到元素位置
        if not korea_element.is_visible():
            korea_element.scroll_into_view_if_needed()
            self.page.wait_for_timeout(1000)
        
        # 点击Korea tab
        korea_element.click()
        log.info("已点击Korea tab")
        
        return True

    def click_usa_tab(self):
        """
        点击USA tab
        """
        log.info("开始点击USA tab")
        
        # 使用get_by_test_id定位元素
        usa_element = self.page.get_by_test_id(dweb_mkpl_global_ele.ele_usa_store)
        if usa_element.count() == 0:
            log.warning("未找到USA tab元素")
            return False
        
        # 滚动到元素位置
        if not usa_element.is_visible():
            usa_element.scroll_into_view_if_needed()
            self.page.wait_for_timeout(1000)
        
        # 点击USA tab
        usa_element.click()
        log.info("已点击USA tab")
        
        return True

    def click_others_tab(self):
        """
        点击Others tab
        """
        log.info("点击Others tab")
        
        # 使用get_by_test_id定位元素
        others_element = self.page.get_by_test_id(dweb_mkpl_global_ele.ele_others_store)
        if others_element.count() == 0:
            log.warning("未找到Others tab元素")
            return False
        
        # 滚动到元素位置
        if not others_element.is_visible():
            others_element.scroll_into_view_if_needed()
            self.page.wait_for_timeout(1000)
        
        # 点击Others tab
        others_element.click()
        log.info("已点击Others tab")
        
        return True

    def click_all_stores_tab(self):
        """
        点击All stores tab
        """
        log.info("开始点击All stores tab")
        
        # 使用get_by_test_id定位元素
        all_stores_element = self.page.get_by_test_id(dweb_mkpl_global_ele.ele_all_stores)
        if all_stores_element.count() == 0:
            log.warning("未找到All stores tab元素")
            return False
        
        # 滚动到元素位置
        if not all_stores_element.is_visible():
            all_stores_element.scroll_into_view_if_needed()
            self.page.wait_for_timeout(1000)
        
        # 点击All stores tab
        all_stores_element.click()
        log.info("已点击All stores tab")
        
        return True

    def click_tabs_in_sequence(self):
        """
        按指定顺序点击所有tab：Japan -> Korea -> USA -> Others -> All stores
        每次点击间隔5秒
        """
        log.info("开始按顺序点击所有tab")
        
        results = {
            "japan": False,
            "korea": False,
            "usa": False,
            "others": False,
            "all_stores": False
        }
        
        # 1. 点击Japan tab
        log.info("步骤1: 点击Japan tab")
        results["japan"] = self.click_japan_tab()
        self.page.wait_for_timeout(5000)  # 等待5秒
        
        # 2. 点击Korea tab
        log.info("步骤2: 点击Korea tab")
        results["korea"] = self.click_korea_tab()
        self.page.wait_for_timeout(5000)  # 等待5秒
        
        # 3. 点击USA tab
        log.info("步骤3: 点击USA tab")
        results["usa"] = self.click_usa_tab()
        self.page.wait_for_timeout(5000)  # 等待5秒
        
        # 4. 点击Others tab
        log.info("步骤4: 点击Others tab")
        results["others"] = self.click_others_tab()
        self.page.wait_for_timeout(5000)  # 等待5秒
        
        # 5. 点击All stores tab
        log.info("步骤5: 点击All stores tab")
        results["all_stores"] = self.click_all_stores_tab()
        
        # 统计结果
        successful_clicks = sum(1 for success in results.values() if success)
        total_clicks = len(results)
        
        log.info("所有tab点击操作完成")
        log.info(f"点击结果统计: {successful_clicks}/{total_clicks} 成功")
        log.info(f"详细结果: {results}")
        
        return results

    def execute_comprehensive_test_flow(self):
        """
        执行完整的综合测试流程（仅包含Tab点击）
        """
        log.info("开始执行综合测试流程")

        results = {
            "page_load": False,
            "tab_clicks": None,
            "overall_success": False
        }

        # 步骤1: 等待页面加载完成 5秒
        log.info("步骤1: 等待页面加载完成 5秒")
        self.wait_for_page_load(5)
        results["page_load"] = True

        # 步骤2: 分别点击 Japan Korea USA Others Tab，每个tab中间间隔5秒
        log.info("步骤2: 分别点击 Japan Korea USA Others Tab")
        tab_results = self.click_tabs_in_sequence()
        results["tab_clicks"] = tab_results

        # 判断整体测试是否成功
        tab_success = bool(tab_results and any(tab_results.values()))

        overall_success = (
            results["page_load"] and
            tab_success
        )

        results["overall_success"] = overall_success

        # 输出测试总结
        log.info("综合测试流程完成")
        log.info(f"测试结果总结:")
        log.info(f"  - 页面加载: {'成功' if results['page_load'] else '失败'}")
        log.info(f"  - Tab点击: {'成功' if tab_success else '失败'}")
        log.info(f"  - 整体结果: {'成功' if overall_success else '失败'}")

        return results
