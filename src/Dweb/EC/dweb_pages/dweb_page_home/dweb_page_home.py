from typing import List, Literal

from playwright.sync_api import Page, Locator

from src.Dweb.EC.dweb_ele.dweb_category.dweb_category_ele import *
from src.Dweb.EC.dweb_ele.dweb_home.dweb_home_ele import *
from src.api.zipcode import switch_zipcode
from src.common.commfunc import empty_cart
from src.common.commonui import close_advertise_on_home, scroll_one_page_until
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log
from src.Dweb.EC.dweb_pages.dweb_common_page import DWebCommonPage


class DWebHomePage(DWebCommonPage):
    """
    该构造方法包含以下功能：
    1. 清空购物车
    2. 进入主页，校验zipcode，如果zipcode不是98011，则切换zipcode
    3. 关闭首页广告弹窗
    """
    def __init__(self, page: Page, header, browser_context):
        super().__init__(page, header)
        self.bc = browser_context

        # 清空购物车
        try:
            empty_cart(self.header)
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))

        self.page.goto(TEST_URL)
        self.page.wait_for_timeout(6000)
        close_advertise_on_home(self.page)
        zipcode = self.page.locator("//div[@id='changeZipCode']/div[position()=1]").text_content()
        if zipcode != '98011':
            switch_zipcode(headers=self.header)
            self.page.reload()
        self.page.wait_for_timeout(10000)
        close_advertise_on_home(self.page)
        self.page.screenshot(path="./screenshots/home.png")

    def close_time_banner(self):
        """
        该构造方法包含以下功能：
        1. 关闭首页顶部的time banner
        """
        if self.page.locator("#timeBanner img").all():
            self.page.locator("#timeBanner img").click()
            self.page.wait_for_timeout(2000)

    def home_switch_specific_store(self, store_name):
        """
        该构造方法包含以下功能：
        1. 根据传入的store_name，切换store， store包含：Chinese, Japanese, Korean, Vietnamese, Filipino, Explorer这6种store
        """

        self.page.wait_for_selector(ele_home_store_selection)
        self.FE.ele(ele_home_store_selection).click()
        self.FE.ele(f"//div[text()='{store_name}']").click()
        # self.page.wait_for_load_state('networkidle', timeout=60000)
        self.page.wait_for_timeout(5000)

    def click_all_cities_on_bottom_home_page(self, city_name):
        """
        该构造方法包含以下功能：
        此方法只在PC端的页面存在，根据传入的city_name, 点击页面底部的城市链接，跳转到对应的页面
        """
        with self.bc.expect_page() as city_page:
            self.page.locator(f"//a[text()='{city_name}']").click()
            self.page.wait_for_timeout(3000)
        new_page: Page = city_page.value
        return new_page


    def download_app(self, platform_name: Literal["ios", "android"]):
        """
        该构造方法包含以下功能：
        此方法只在PC端的页面存在，根据传入的platform_name, 点击下载app链接，如果是ios，则跳转到下载ios app的页面，如果是android，则跳转到下载android app的页面
        """
        if platform_name == "ios":
            self.page.locator("a[aria-label='download ios']").click()
            self.page.wait_for_timeout(2000)
        elif platform_name == "android":
            self.page.locator("a[aria-label='download android']").click()
        else:
            log.info("输入的platform名称不对")

    def change_zipcode(self, zipcode="98011"):
        """
        该构造方法包含以下功能：
        1. 根据传入的zipcode, 输入zipcode并点击confirm, 用于PC页面的切换zipcode
        """
        self.FE.ele(ele_home_zipcode).click()
        self.page.wait_for_timeout(1500)
        self.FE.ele(ele_home_enter_your_zipcode).fill(zipcode)
        self.FE.ele(ele_home_confirm_zipcode).click()
        self.page.wait_for_load_state("networkidle", timeout=60000)
        close_advertise_on_home(self.page)

    ######################################################################

    def goto_category(self, category):
        """
        该构造方法包含以下功能：
        1. 根据传入的category, 从首页点击category div，进入category所对应的页面，校验页面元素并加购
        """

        category_selector = f"//span[text()='{category}']" if r"'" not in category else f'//span[text()="{category}"]'
        self.FE.ele(category_selector).click()
        # self.page.wait_for_load_state("networkidle", timeout=60000)
        # 不加等待，页面太快，容易点乱
        self.page.wait_for_timeout(2500)

        # 校验页面
        self.check_category_page()
        # 将商品加入购物车
        self.add_category_result_to_cart(category)
        self.page.wait_for_timeout(2000)

    def switch_same_zipcode_and_check_products(self):
        """ 此方法不具有通用性，不建议AI使用 """
        self.page.goto(TEST_URL)
        self.page.wait_for_selector(ele_home_zipcode)

        # 切换zipcode前获取商品信息
        products_title_before = self.get_product_titles(index=10)
        # 切换相同zipcode
        self.change_zipcode()
        # 切换zipcode后获取商品信息
        products_title_after = self.get_product_titles(index=10, is_scroll=False)
        # 校验前5个商品信息相同
        assert set(products_title_before) == set(products_title_after), f"before={products_title_before}, after={products_title_after}"

    def switch_different_zipcode_and_check_products(self):
        """ 此方法不具有通用性，不建议AI使用 """
        self.page.goto(TEST_URL)
        close_advertise_on_home(self.page)
        self.page.wait_for_selector(ele_home_zipcode)

        # 切换zipcode前获取商品信息
        products_title_before = self.get_product_titles(index=10)
        # 切换不同zipcode
        self.change_zipcode("00501")
        # 切换zipcode后获取商品信息
        products_title_after = self.get_product_titles(index=10, is_scroll=False)
        # 校验前5个商品信息相同
        assert set(products_title_before) != set(products_title_after), f"before={products_title_before}, after={products_title_after}"
        # 切回98011
        self.change_zipcode()


    def get_product_titles(self, index, is_scroll=True):
        """
        该构造方法包含以下功能：
        1. 获取首页商品的title，不区分合集
        """
        # 往下稍微翻一下，否则可能直接获取不了
        self.page.wait_for_load_state("domcontentloaded", timeout=60000)
        # 第二次调用的时候不能翻页，否则数量对不住
        if is_scroll:
            self.page.evaluate('window.scrollBy(0, window.innerHeight/4)')
        self.page.wait_for_timeout(5000)
        all_home_products_title = self.FE.eles(ele_home_products_title)
        assert all_home_products_title, f"当前Home页面没有商品，未获取商品的title, all_home_products_title={all_home_products_title}"
        titles = []
        for _index, title in enumerate(all_home_products_title):
            titles.append(title.text_content())
            if _index >= index:
                break
        return titles


    # 1. check category page
    def check_category_page(self):
        """
        该构造方法包含以下功能：
        1. 校验分类页面的元素是否存在，主要是result_div和result_product_div
        """
        assert self.FE.ele(ele_category_result_div).is_visible() or self.page.locator('div[data-module="cm_product_line"]').is_enabled()
        # 在不加filter的情况下，商品数量应该大于0
        assert self.FE.eles(ele_category_result_product_div)

    def add_category_result_to_cart(self, category):
        """
        该构造方法包含以下功能：
        1. 根据传入的category, 从首页点击category div，进入category所对应的页面，加购category页面的商品
        """
        category_results = self.FE.eles(ele_category_add_to_cart)
        assert category_results, f"{category}下没有商品"
        for index, item in enumerate(category_results):
            try:
                item.evaluate('(item) => item.click()')
                # item.click()
            except Exception as e:
                log.info("加购按钮点击失败" + str(e))
            # self.page.reload()
            self.page.wait_for_timeout(1000)
            if index == 3:
                break

    def home_switch_specific_store_and_check(self, store_name):
        """
        该构造方法包含以下功能：
        1. 根据传入的store_name，首页切换不同的store
        与MS上的用例对应: 【110401】 explorer store下editor pick组件样式验证
        2. 校验首页editor's pick合集的商品，包含商品卡片，title, price, tags_container
        3. 加购editor's pick合集的商品
        """
        self.page.wait_for_selector(ele_home_store_selection)
        self.FE.ele(ele_home_store_selection).click()
        self.FE.ele(f"//div[text()='{store_name}']").click()
        # self.page.wait_for_load_state('networkidle', timeout=60000)
        self.page.wait_for_timeout(5000)
        close_advertise_on_home(self.page)
        self.page.evaluate('window.scrollBy(0, window.innerHeight)')
        self.page.wait_for_load_state('networkidle', timeout=60000)


        # 检查editors' pick
        all_editors_card_div: List[Locator] = self.page.locator(ele_home_editors_pick_card_div).all()
        assert all_editors_card_div, f"explore store没有editor's pick卡片"
        for _index, item in enumerate(all_editors_card_div):
            # 1. 校验tags_container卡片，不是每个元素都有
            # assert item.locator("//div[starts-with(@class, 'ProductCard_tagsContainer')]").is_enabled()
            if item.locator("//div[starts-with(@class, 'ProductCard_tagsContainer')]").is_visible():
                assert item.locator("//div[starts-with(@class, 'ProductCard_tagsContainer')]").text_content()
            # 2. 校验价格卡片，每个元素都有
            assert item.locator("//div[starts-with(@class, 'ProductCard_price')]").is_visible()
            # 3. 校验title卡片，每个元素都有
            assert item.locator("//div[starts-with(@class, 'ProductCard_title')]").is_visible()
            # 4. 校验每个title的长度大于0
            assert item.locator("//div[starts-with(@class, 'ProductCard_title')]").text_content()

            # 5. 点击加购按钮
            if len(item.locator("//i[@data-role]").all()) == 1:
                item.locator("//i[@data-role]").click()
            self.page.wait_for_timeout(3000)

            if _index > 1:
                # 进入pdp
                item.locator("//img[contains(@class, 'ProductCard_productImg')]").click()
                self.page.wait_for_timeout(4000)
                assert self.page.locator("//div[starts-with(@class, 'Header_name')]//h2").is_visible()
                assert 'product' in self.page.url
                self.page.go_back(wait_until="networkidle", timeout=60000)
                break

    def home_banner_check(self):
        """
        该构造方法包含以下功能：
        1. 点击banner右侧箭头，使轮播图向右滑动，查看更多的轮播图
        2. 点击banner图片，进入具体的banner对应的页面，校验页面元素，如果页面有商品，则加购商品
        """
        self.page.locator(ele_home_banner_active).hover()
        for i in range(10):
            self.page.locator(ele_home_right_shift_button).click()
            self.page.wait_for_timeout(2000)

        self.page.reload()
        self.page.wait_for_timeout(3000)
        main_banner = self.page.get_by_test_id("btn-main-banner-img-0").element_handles()
        # main_banner[0].click()
        self.page.locator('span[class="swiper-slide swiper-slide-active"]').click()
        self.page.wait_for_timeout(10000)
        if self.page.get_by_test_id("alternate-ingridients").all():
            all_product_cards = self.page.get_by_test_id("alternate-ingridients")
            assert all_product_cards, f"此banner中不包含商品"
            all_add_to_cart_button = all_product_cards.locator('button[class="new-add-btn"]').element_handles()
            for index, i in enumerate(all_add_to_cart_button):
                if index == 3:
                    break
                i.click()
                self.page.wait_for_timeout(1000)
        else:
            log.info("此banner没有可售商品")




    def tb1_home(self):
        # 1. scroll to the element "featured reviews"
        scroll_one_page_until(self.page, "div[data-testid='mod-reviews-carousel-Featured-Reviews']")

        # 2. add each collections' products to cart
        for coll_name in ["mod-item-carousel-Editor's-Pick", "mod-item-carousel-New-Arrivals", "mod-item-carousel-Bestsellers", r"mod-tab-item-carousel-Global+-Top-charts", "mod-tab-item-carousel-Fresh-Daily", "mod-item-carousel-Everyday-deals", "mod-item-carousel-Recommended-For-You"]:
            self._add_products_to_cart_for_all_collections(coll_name)

        # 6. click cart button to navigate to cart page
        self.page.get_by_test_id("wid-mini-cart").click()

        # 7. 购物车校验
        self._cart_page_check()

        # 8. checkout校验
        self._checkout()

        # 9. place order
        self.place_order()

    def _add_products_to_cart_for_all_collections(self, collections_name):
        # 3. goto the section collection presents
        if self.page.get_by_test_id(collections_name).all():
            self.page.get_by_test_id(collections_name).scroll_into_view_if_needed()
            # 4. find add_to_cart buttons belongs to the collection
            add_to_cart_list = self.page.get_by_test_id(collections_name).get_by_test_id("wid-atc-plus").element_handles()
            product_card_list = self.page.get_by_test_id(collections_name).get_by_test_id("wid-product-card-container").all()
            # 5. click add_to_cart button 5 times for each collection
            for index, item in enumerate(product_card_list):
                if item.locator("//span[text()='Pre-sale']").all():
                    continue
                if item.get_by_test_id("btn-atc-plus").all():
                    item.get_by_test_id("btn-atc-plus").click()
                    self.page.wait_for_timeout(1000)
                if index == 4:
                    break
            self.page.wait_for_timeout(3000)

    def _cart_page_check(self):
        self.page.wait_for_timeout(10000)
        self.cart_page_common_check()
        assert self.page.get_by_test_id("wid-cart-summary-normal-0").is_visible()
        # 1. 获取所有的cart
        all_cart_locators = self.page.locator('#cart-main [data-testid^="wid-cart-summary"]').all()
        for cart in all_cart_locators:
            cart_goods_cards = cart.get_by_test_id("wid-cart-section-goods").all()
            for card in cart_goods_cards:
                if card.locator("//span[text()='Gift']").all():
                    continue
                # 断言缩略图可显示
                assert card.locator("div[data-component='CroppedImage']").is_visible()
                log.info("products name===>" + card.locator("span[class='align-middle']").text_content())
                assert card.locator("span[class='align-middle']").text_content()
                log.info("products price===>" + card.locator("span[class^='GoodsInCart_priceUsed']").text_content())
                assert "$" in card.locator("span[class^='GoodsInCart_priceUsed']").text_content()
                if card.get_by_test_id("btn-atc-plus").all():
                    card.get_by_test_id("btn-atc-plus").click()
                if card.get_by_test_id("btn-atc-minus").all():
                    card.get_by_test_id("btn-atc-minus").click()

        # 2. 校验右边的summary


    def _checkout(self):
        self.page.get_by_test_id("wid-cart-summary-checkout").click()
        # select all carts to checkout
        if self.page.get_by_test_id("wid-cart-select-modal-select-all-btn").all():
            self.page.get_by_test_id("wid-cart-select-modal-select-all-btn").click()
            self.page.wait_for_selector(selector='button[data-testid="wid-cart-select-modal-checkout-btn"]', state="attached").is_enabled()
            self.page.get_by_test_id("wid-cart-select-modal-checkout-btn").click()

        self.page.wait_for_timeout(3000)

    def place_order(self):
        self.page.wait_for_timeout(3000)
        if self.page.get_by_test_id("wid-upsell-continue-to-checkout").all():
            self.page.get_by_test_id("wid-upsell-continue-to-checkout").click()
            self.page.wait_for_timeout(3000)
        self.page.get_by_test_id("wid-checkout-btn").click()
        if self.page.get_by_test_id("wid-checkout-full-name-modal-confirm-checkbox").all():
            self.page.get_by_test_id("wid-checkout-full-name-modal-confirm-checkbox").click()
            self.page.get_by_test_id("wid-checkout-full-name-modal-btn-place").click()

    def add_products_from_home(self, count: int = 2):
        """
        在首页加购指定数量的商品

        Args:
            count: 要加购的商品数量，默认2个

        Returns:
            int: 实际加购的商品数量
        """
        try:
            # 等待页面加载完成
            self.page.wait_for_timeout(5000)

            # 查找首页所有加购按钮
            home_add_buttons = self.page.get_by_test_id("btn-atc-plus").all()
            log.info(f"首页找到 {len(home_add_buttons)} 个加购按钮")

            added_count = 0
            for index, add_btn in enumerate(home_add_buttons):
                try:
                    if add_btn.is_visible() and add_btn.is_enabled():
                        add_btn.click()
                        self.page.wait_for_timeout(1500)  # 等待加购完成
                        added_count += 1
                        log.info(f"首页成功加购第 {added_count} 个商品")

                        # 达到指定数量后停止
                        if added_count >= count:
                            break
                except Exception as e:
                    log.warning(f"首页第 {index + 1} 个加购按钮点击失败: {str(e)}")
                    continue

            log.info(f"首页成功加购 {added_count} 个商品")
            return added_count
        except Exception as e:
            log.error(f"首页加购商品失败: {str(e)}")
            return 0

    def navigate_to_global_plus(self):
        """
        点击Global+按钮进入Global+页面

        Returns:
            bool: 是否成功进入Global+页面
        """
        try:
            # 点击Global+按钮 (新老web元素定位不一样，新的样式没有test_id)
            global_plus_btn = self.page.get_by_test_id("wid-direct-from-japan").all()
            assert global_plus_btn, "Global+按钮不可见"

            global_plus_btn[0].click()
            self.page.wait_for_timeout(3000)

            # 等待Global+页面加载完成
            self.page.wait_for_timeout(5000)
            # 处理not now
            if self.page.locator("//div[text()='Not now']").all():
                self.page.locator("//div[text()='Not now']").click()
                self.page.wait_for_timeout(2000)
            log.info("成功进入Global+页面")
            return True
        except Exception as e:
            log.error(f"进入Global+页面失败: {str(e)}")
            return False

    def add_products_from_global_plus(self, count: int = 2):
        """
        在Global+页面加购指定数量的商品

        Args:
            count: 要加购的商品数量，默认2个

        Returns:
            int: 实际加购的商品数量
        """
        try:
            # 查找Global+页面所有加购按钮
            global_add_buttons = self.page.get_by_test_id("btn-atc-plus").all()
            log.info(f"Global+页面找到 {len(global_add_buttons)} 个加购按钮")

            added_count = 0
            for index, add_btn in enumerate(global_add_buttons):
                try:
                    if add_btn.is_visible() and add_btn.is_enabled():
                        add_btn.click()
                        self.page.wait_for_timeout(1500)  # 等待加购完成
                        added_count += 1
                        log.info(f"Global+页面成功加购第 {added_count} 个商品")

                        # 达到指定数量后停止
                        if added_count >= count:
                            break
                except Exception as e:
                    log.warning(f"Global+页面第 {index + 1} 个加购按钮点击失败: {str(e)}")
                    continue

            log.info(f"Global+页面成功加购 {added_count} 个商品")
            return added_count
        except Exception as e:
            log.error(f"Global+页面加购商品失败: {str(e)}")
            return 0

    def create_multi_cart_by_adding_products(self):
        """
        通过在首页和Global+页面加购商品来创建多种类型购物车

        Returns:
            dict: 包含加购结果的字典
        """
        result = {
            'home_added': 0,
            'global_added': 0,
            'total_added': 0,
            'success': False
        }

        try:
            # 在首页加购2个商品
            home_added = self.add_products_from_home(2)
            result['home_added'] = home_added

            # 进入Global+页面
            if self.navigate_to_global_plus():
                # 在Global+页面加购2个商品
                global_added = self.add_products_from_global_plus(2)
                result['global_added'] = global_added

            result['total_added'] = result['home_added'] + result['global_added']
            result['success'] = result['total_added'] >= 4

            log.info(f"多购物车创建结果: {result}")
            return result
        except Exception as e:
            log.error(f"创建多购物车失败: {str(e)}")
            return result

    def click_cart_button(self):
        """
        点击首页Cart按钮进入购物车页面

        Returns:
            bool: 是否成功点击Cart按钮
        """
        try:
            # 点击Cart按钮
            cart_btn = self.page.get_by_test_id("wid-mini-cart")
            assert cart_btn.is_visible(timeout=5000), "Cart按钮不可见"

            cart_btn.click()
            self.page.wait_for_timeout(3000)

            # 把光标移走，否则购物车下拉框不消失
            self.page.hover("//a[@aria-label='Weee! Logo']")
            # 等待购物车页面加载完成
            self.page.wait_for_timeout(3000)
            log.info("成功点击Cart按钮，进入购物车页面")
            return True
        except Exception as e:
            log.error(f"点击Cart按钮失败: {str(e)}")
            return False


def handle_home_activity_popup(self):
    """
    访问首页并处理活动弹窗
    1. 访问首页 https://www.sayweee.com/en?referral_id=9664862
    2. 检查是否有活动弹窗
    3. 如果没有弹窗，切换zipcode (98011, 95008, 46311, 99348)
    4. 处理弹窗：关闭或点击跳转
    """
    # 访问首页
    self.page.goto("https://www.sayweee.com/en?referral_id=9664862")
    self.page.wait_for_timeout(6000)

    # 检查是否有活动弹窗
    popup_exists = self.check_activity_popup()

    if not popup_exists:
        # 尝试切换不同的zipcode来触发弹窗
        zipcodes = ["98011", "95008", "46311", "99348"]
        for zipcode in zipcodes:
            self.switch_zipcode(zipcode)
            self.page.wait_for_timeout(3000)
            popup_exists = self.check_activity_popup()
            if popup_exists:
                break

    return popup_exists


def check_activity_popup(self):
    """检查活动弹窗是否存在"""
    try:
        popup = self.page.locator(ele_home_activity_popup_image).first
        return popup.is_visible()
    except:
        return False


def close_activity_popup(self):
    """关闭活动弹窗"""
    try:
        close_button = self.page.locator(ele_home_activity_popup_close_button).first
        if close_button.is_visible():
            close_button.click()
            self.page.wait_for_timeout(2000)
            return True
    except:
        return False


def click_activity_popup(self):
    """点击活动弹窗跳转到活动页"""
    try:
        popup_image = self.page.locator(ele_home_activity_popup_image).first
        if popup_image.is_visible():
            popup_image.click()
            self.page.wait_for_timeout(3000)
            return True
    except:
        return False


def switch_zipcode(self, zipcode):
    """切换zipcode"""
    try:
        # 点击zipcode区域
        self.page.locator(ele_home_zipcode).click()
        self.page.wait_for_timeout(2000)

        # 输入新的zipcode
        self.page.locator(ele_home_enter_your_zipcode).fill(zipcode)

        # 点击确认按钮
        self.page.locator(ele_home_confirm_zipcode).click()
        self.page.wait_for_timeout(3000)
    except Exception as e:
        log.info(f"切换zipcode失败: {str(e)}")