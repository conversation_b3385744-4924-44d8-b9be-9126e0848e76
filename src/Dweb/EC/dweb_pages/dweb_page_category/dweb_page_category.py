from playwright.sync_api import Locator
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log
from playwright.sync_api import Page
from src.Dweb.EC.dweb_ele.dweb_home import dweb_home_ele
from src.Dweb.EC.dweb_ele.dweb_category import dweb_category_ele
from src.common.commonui import scroll_one_page_until, close_advertise_on_home
from src.Dweb.EC.dweb_pages.dweb_common_page import DWebCommonPage


class DWebCategorypage(DWebCommonPage):
    def __init__(self, page: Page, header, browser_context, zipcode: str = "98011"):
        super().__init__(page, header)
        self.bc = browser_context
        # 进入首页
        self.page.goto(TEST_URL)
        # # 获取顶部语言
        # if not self.page.get_by_test_id("wid-language").locator(
        #         "//span[text()='English'and contains(@class,'Header')]").all():
        #     # 切换为英文
        #     pass
        # # 获取顶部zipocde
        # page_zipcode = self.page.locator(home_elements.ele_zipcode).text_content()
        # if page_zipcode != zipcode:
        #     switch_zipcode(headers=self.header)
        #     self.page.reload()
        self.page.wait_for_timeout(10000)
        close_advertise_on_home(self.page)

    def go_to_special_category_from_hone(self, category_selector):
        """
        从首页进入特定分类页面，增强错误处理和备选方案
        """
        try:
            # 确保在首页
            if not self.page.url.endswith(TEST_URL.split("//")[1]):
                log.info(f"当前不在首页，正在跳转到首页...")
                self.page.goto(TEST_URL)
                self.page.wait_for_timeout(3000)
            
            # 关闭可能的弹窗
            # close_popup_on_home(self.page)
            # close_advertise_on_home(self.page)
            
            # 尝试多种方式查找Deals元素
            deals_found = False
            
            # 方法1: 使用提供的选择器
            try:
                deals_element = self.page.wait_for_selector(category_selector, timeout=5000)
                if deals_element and deals_element.is_visible():
                    log.info("找到Deals元素，使用提供的选择器")
                    deals_element.click()
                    deals_found = True
            except Exception as e:
                log.warning(f"使用提供的选择器未找到Deals元素: {str(e)}")
            
            # 方法2: 使用备选选择器
            if not deals_found:
                backup_selectors = [
                    "//a[contains(@href,'/category/sale')]",
                    "//a[contains(text(),'Deals')]",
                    "//a[contains(text(),'优惠')]",
                    "//a[@data-testid='deals-link']",
                    "//div[contains(@class,'HeaderWithCategory')]//a[contains(text(),'Deals')]"
                ]
                
                for selector in backup_selectors:
                    try:
                        deals_element = self.page.wait_for_selector(selector, timeout=3000)
                        if deals_element and deals_element.is_visible():
                            log.info(f"找到Deals元素，使用备选选择器: {selector}")
                            deals_element.click()
                            deals_found = True
                            break
                    except Exception as e:
                        log.debug(f"使用备选选择器 {selector} 未找到Deals元素: {str(e)}")
            
            # 方法3: 直接导航到Deals页面URL
            if not deals_found:
                log.info("未找到Deals元素，直接导航到Deals页面URL")
                self.page.goto(f"{TEST_URL}/category/sale?filter_sub_category=sale")
                deals_found = True
            
            # 等待页面加载
            self.page.wait_for_load_state("networkidle", timeout=30000)
            self.page.wait_for_timeout(3000)
            
            # 验证是否成功进入Deals页面
            current_url = self.page.url
            if "category/sale" in current_url or "deals" in current_url.lower():
                log.info(f"成功进入Deals页面: {current_url}")
                return True
            else:
                log.warning(f"可能未成功进入Deals页面，当前URL: {current_url}")
                return False
            
        except Exception as e:
            log.error(f"进入Deals分类页面失败: {str(e)}")
            # 尝试直接导航到Deals页面作为最后的备选方案
            try:
                self.page.goto(f"{TEST_URL}/category/sale?filter_sub_category=sale")
                self.page.wait_for_load_state("networkidle", timeout=30000)
                log.info("通过直接URL导航进入Deals页面")
                return True
            except Exception as e2:
                log.error(f"直接导航到Deals页面也失败: {str(e2)}")
                return False

    def category_filter_delivery_type(self, filter_delivery_type: Locator):
        """
        该方法包含以下功能：
        1. 分类页点击勾选filter_delivery_type对应的filter来筛选不同的搜索结果
        """
        filter_delivery_type.click()
        self.page.wait_for_timeout(2000)

    def category_filter_delivery_type_check(self, filter_delivery_type: Locator):
        """
        该方法包含以下功能：
        1. 分类页点击勾选filter_delivery_type对应的filter来筛选不同的搜索结果
        """
        filter_delivery_type.click()
        self.page.wait_for_timeout(2000)

    def category_filter_delivery_type_uncheck(self, filter_delivery_type):
        """
        该方法包含以下功能：
        1. 分类页点击[取消勾选]filter_delivery_type对应的filter，来筛选不同的搜索结果
        """
        filter_delivery_type.uncheck()
        self.page.wait_for_timeout(2000)

    def category_filter_product_type_check(self, filter_product_type):
        """
        该方法包含以下功能：
        1. 分类页点击勾选filter:Product type, 根据产品类型来筛选产品，获得不同的搜索结果
        """
        filter_product_type.check()
        self.page.wait_for_timeout(2000)

    def category_filter_product_type_uncheck(self, filter_product_type):
        """
        该方法包含以下功能：
        1. 分类页点击取消勾选filter:Product type, 根据产品类型来筛选产品，获得不同的搜索结果
        """
        filter_product_type.uncheck()
        self.page.wait_for_timeout(2000)

    def special_category_filter_sub_category(self, sub_category):
        """
        该方法包含以下功能：
        1. 根据传入的特殊分类页点击切换子分类
        """
        self.FE.ele(sub_category).click()
        self.page.wait_for_timeout(2000)

    def add_product_to_cart(self, product_id):
        """
        该方法包含以下功能：
        1. 根据传入的product_id, 加购指定商品
        """
        product_id.click()
        self.page.wait_for_timeout(2000)

    def go_to_cart_from_category(self):
        """
        该方法包含以下功能：
        1. 点击购物车按钮，进入购物车页面
        """
        self.page.get_by_test_id("wid-mini-cart").click()
        self.page.wait_for_load_state("networkidle", timeout=60000)

    def add_to_local_product_cart_from_category(self):
        """
        该方法包含以下功能：
        1. 分类页delivery_type为local类型的商品
        """
        # 进入deals分类页
        self.go_to_special_category_from_hone(dweb_home_ele.ele_deals)
        # 滚动到指定位置
        scroll_one_page_until(self.page, dweb_category_ele.ele_filter_reset)
        # 点击Delivery type = Local Delivery
        delivery_type_local = self.page.get_by_test_id(dweb_category_ele.local_delivery_test_id)
        self.category_filter_delivery_type(delivery_type_local)

        self.page.wait_for_timeout(2000)
        product_ids = self.page.query_selector_all("//div[@data-testid='btn-atc-plus']")
        assert product_ids, f"本地售卖没有商品"
        for index, item in enumerate(product_ids):
            try:
                self.add_product_to_cart(item)
            except Exception as e:
                log.info("按钮加购失败" + str(e))
            if index == 1:
                break

        # 滚动到指定位置
        scroll_one_page_until(self.page, dweb_category_ele.ele_filter_reset)
        # 再调一次，点击取消Delivery type = Local Delivery
        self.category_filter_delivery_type(delivery_type_local)

    def add_to_pantry_product_cart_from_category(self):
        """
        该方法包含以下功能：
        1. 在分类页加购pantry类型的商品到购物车
        """
        # 进入deals分类页
        self.go_to_special_category_from_hone(dweb_home_ele.ele_deals)
        # 滚动到指定位置
        scroll_one_page_until(self.page, dweb_category_ele.ele_filter_reset)
        # 点击Delivery type = pantry
        delivery_type_pantry = self.page.get_by_test_id(dweb_category_ele.pantry_delivery_test_id)
        # 滚动到指定位置
        scroll_one_page_until(self.page, dweb_category_ele.ele_filter_reset)
        # 点击pantry
        self.category_filter_delivery_type(delivery_type_pantry)
        # 加购pantry售卖第一个商品
        product_ids = self.page.query_selector_all("//div[@data-testid='btn-atc-plus']")
        assert product_ids, f"本地售卖没有商品"
        for index, item in enumerate(product_ids):
            try:
                self.add_product_to_cart(item)
            except Exception as e:
                log.info("按钮加购失败" + str(e))
            if index == 1:
                break
        # 滚动到指定位置
        scroll_one_page_until(self.page, dweb_category_ele.ele_filter_reset)
        # 再调一次，点击取消Delivery type = pantry
        self.category_filter_delivery_type(delivery_type_pantry)

    def add_to_global_product_cart_from_category(self):
        """
        该方法包含以下功能：
        1. 分类页加购global类型的商品
        """
        # 进入deals分类页
        self.go_to_special_category_from_hone(dweb_home_ele.ele_deals)
        # 滚动到指定位置
        scroll_one_page_until(self.page, dweb_category_ele.ele_filter_reset)
        # 点击Delivery type = global
        delivery_type_global = self.page.get_by_test_id(dweb_category_ele.global_delivery_test_id)
        # 滚动到指定位置
        scroll_one_page_until(self.page, dweb_category_ele.ele_filter_reset)
        self.category_filter_delivery_type(delivery_type_global)
        # 加购global售卖第一个商品
        product_ids = self.page.query_selector_all("//div[@data-testid='btn-atc-plus']")
        assert product_ids, f"本地售卖没有商品"
        for index, item in enumerate(product_ids):
            try:
                self.add_product_to_cart(item)
            except Exception as e:
                log.info("按钮加购失败" + str(e))
            if index == 1:
                break
        # 滚动到指定位置
        scroll_one_page_until(self.page, dweb_category_ele.ele_filter_reset)
        # 再调一次，点击取消Delivery type = global
        self.category_filter_delivery_type(delivery_type_global)

    def add_to_alcohol_product_cart_from_category(self):
        """
        该方法包含以下功能：
        1. 分类页加购alcohol类型的商品
        """
        # 进入deals分类页
        self.go_to_special_category_from_hone(dweb_home_ele.ele_deals)
        # 滚动到指定位置
        scroll_one_page_until(self.page, dweb_category_ele.ele_filter_reset)
        # 点击Delivery type = Local Delivery
        delivery_type_local = self.page.get_by_test_id(dweb_category_ele.local_delivery_test_id)
        self.category_filter_delivery_type(delivery_type_local)

        self.page.wait_for_timeout(2000)
        product_ids = self.page.query_selector_all("//div[@data-testid='btn-atc-plus']")
        assert product_ids, f"本地售卖没有商品"
        for index, item in enumerate(product_ids):
            try:
                self.add_product_to_cart(item)
            except Exception as e:
                log.info("按钮加购失败" + str(e))
            if index == 1:
                break

        # 滚动到指定位置
        scroll_one_page_until(self.page, dweb_category_ele.ele_filter_reset)
        # 再调一次，点击取消Delivery type = Local Delivery
        self.category_filter_delivery_type(delivery_type_local)
        # 点击Delivery type = pantry
        delivery_type_pantry = self.page.get_by_test_id(dweb_category_ele.pantry_delivery_test_id)
        # 滚动到指定位置
        scroll_one_page_until(self.page, dweb_category_ele.ele_filter_reset)
        # 点击pantry
        self.category_filter_delivery_type(delivery_type_pantry)
        # 加购pantry售卖第一个商品
        product_ids = self.page.query_selector_all("//div[@data-testid='btn-atc-plus']")
        assert product_ids, f"本地售卖没有商品"
        for index, item in enumerate(product_ids):
            try:
                self.add_product_to_cart(item)
            except Exception as e:
                log.info("按钮加购失败" + str(e))
            if index == 1:
                break
        # 滚动到指定位置
        scroll_one_page_until(self.page, dweb_category_ele.ele_filter_reset)
        # 再调一次，点击取消Delivery type = pantry
        self.category_filter_delivery_type(delivery_type_pantry)

        # 点击Delivery type = global
        delivery_type_global = self.page.get_by_test_id(dweb_category_ele.global_delivery_test_id)
        # 滚动到指定位置
        scroll_one_page_until(self.page, dweb_category_ele.ele_filter_reset)
        self.category_filter_delivery_type(delivery_type_global)
        # 加购global售卖第一个商品
        product_ids = self.page.query_selector_all("//div[@data-testid='btn-atc-plus']")
        assert product_ids, f"本地售卖没有商品"
        for index, item in enumerate(product_ids):
            try:
                self.add_product_to_cart(item)
            except Exception as e:
                log.info("按钮加购失败" + str(e))
            if index == 1:
                break
        # 滚动到指定位置
        scroll_one_page_until(self.page, dweb_category_ele.ele_filter_reset)
        # 再调一次，点击取消Delivery type = global
        self.category_filter_delivery_type(delivery_type_global)
        # 切换到酒分类
        self.special_category_filter_sub_category(dweb_category_ele.Alcohol)
        # 加购商品
        product_ids = self.page.query_selector_all("//div[@data-testid='btn-atc-plus']")
        assert product_ids, f"本地售卖没有商品"
        for index, item in enumerate(product_ids):
            try:
                self.add_product_to_cart(item)
            except Exception as e:
                log.info("按钮加购失败" + str(e))
            if index == 1:
                break

        # 8. 去购物车结算
        self.go_to_cart_from_category()


    def add_to_many_cart_from_category(self):
        """
        该方法包含以下功能：
        1. 分类页加购Deals类型的商品
        增强错误处理和备选方案
        """
        # 进入deals分类页
        deals_page_success = self.go_to_special_category_from_hone(dweb_home_ele.ele_deals)
        if not deals_page_success:
            log.warning("进入Deals页面可能不成功，尝试直接搜索商品")
            # 备选方案：使用搜索添加商品
            self.page.goto(TEST_URL)
            self.page.wait_for_load_state("networkidle", timeout=30000)
            
            # 关闭可能的弹窗
            close_popup_on_home(self.page)
            close_advertise_on_home(self.page)
            
            # 使用搜索
            try:
                search_input = self.page.get_by_placeholder("Search")
                if search_input.is_visible(timeout=5000):
                    search_input.click()
                    self.page.wait_for_timeout(2000)
                    self.page.keyboard.type("popular")
                    self.page.keyboard.press('Enter')
                    self.page.wait_for_timeout(5000)
                    log.info("使用搜索功能查找商品")
                else:
                    log.warning("未找到搜索框，尝试其他方式")
                    # 尝试点击任意分类
                    self.page.locator("//a[contains(@href,'/category/')]").first.click()
                    self.page.wait_for_timeout(5000)
            except Exception as e:
                log.error(f"搜索商品失败: {str(e)}")
        
        # 尝试滚动页面以确保商品加载
        try:
            self.page.evaluate("window.scrollBy(0, 300)")
            self.page.wait_for_timeout(1000)
            self.page.evaluate("window.scrollBy(0, 300)")
            self.page.wait_for_timeout(1000)
        except Exception as e:
            log.warning(f"滚动页面失败: {str(e)}")
        
        # 尝试使用多种选择器定位加购按钮
        selectors = [
            "//div[@data-testid='btn-atc-plus']",
            "//button[contains(@class, 'add-to-cart')]",
            "//div[contains(@class, 'add-to-cart')]",
            "//div[contains(@class, 'add-button')]",
            "//i[@data-role='addButtonPlusIcon']",
            "//i[contains(@class, 'icon-plus')]"
        ]
        
        product_buttons = []
        for selector in selectors:
            try:
                buttons = self.page.query_selector_all(selector)
                if buttons and len(buttons) > 0:
                    product_buttons = buttons
                    log.info(f"使用选择器 '{selector}' 找到{len(buttons)}个加购按钮")
                    break
            except Exception as e:
                log.warning(f"使用选择器 '{selector}' 定位加购按钮失败: {str(e)}")
        
        if not product_buttons:
            log.error("未找到任何加购按钮，无法添加商品")
            # 截图记录错误状态
            try:
                screenshot_path = f"no_add_buttons_{int(time.time())}.png"
                self.page.screenshot(path=screenshot_path)
                log.info(f"错误截图已保存到: {screenshot_path}")
            except:
                pass
            return False
        
        # 加购商品
        added_count = 0
        for index, item in enumerate(product_buttons):
            if index >= 3:  # 只添加3个商品
                break
            try:
                log.info(f"尝试加购第{index+1}个商品")
                # 确保元素可见
                item.scroll_into_view_if_needed()
                self.page.wait_for_timeout(2000)
                
                # 尝试点击
                try:
                    item.click()
                    self.page.wait_for_timeout(2000)
                    added_count += 1
                    log.info(f"成功加购第{index+1}个商品")
                except Exception as e:
                    log.warning(f"点击加购按钮失败，尝试使用JavaScript: {str(e)}")
                    # 尝试使用JavaScript点击
                    try:
                        self.page.evaluate("(element) => element.click()", item)
                        self.page.wait_for_timeout(2000)
                        added_count += 1
                        log.info(f"使用JavaScript成功加购第{index+1}个商品")
                    except Exception as js_e:
                        log.error(f"使用JavaScript加购也失败: {str(js_e)}")
            except Exception as e:
                log.error(f"加购第{index+1}个商品失败: {str(e)}")
        
        log.info(f"成功加购{added_count}个商品")
        return added_count > 0
