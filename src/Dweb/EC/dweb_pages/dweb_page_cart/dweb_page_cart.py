from playwright.sync_api import Page

from src.Dweb.EC.dweb_ele.dweb_cart.dweb_cart_ele import ele_cart_normal_item_total, ele_cart_banner_normal
from src.Dweb.EC.dweb_ele.dweb_home import dweb_home_ele
from src.Dweb.EC.dweb_ele.dweb_cart import dweb_cart_ele
from src.api.zipcode import switch_zipcode
from src.common.commonui import close_advertise_on_home
from src.config.base_config import TEST_URL
from src.Dweb.EC.dweb_pages.dweb_common_page import DWebCommonPage
from src.config.weee.log_help import log


class DWebCartPage(DWebCommonPage):
    """
    这个类主要封装了PC web端对购物车页面的封装
    """
    def __init__(self, page: Page, header, browser_context, zipcode: str = "98011", page_url: str = None):
        """
        构造方法主要包含以下功能：
        1. 进入购物车页面
        2.如果zipcode不是98011，则切换zipcode
        3.调用close_advertise_on_home(self.page)关掉首页的广告
        """
        super().__init__(page, header)
        self.bc = browser_context
        # 直接进入指定页面
        self.page.goto(TEST_URL + page_url)
        # 获取顶部语言
        self.home_page_switch_lang(lang="English")
        # 获取顶部zipocde
        self.home_page_switch_zipcode(zipcode)

    def start_shopping(self):
        """
        方法包含以下功能：
        1. 点击空购物车的start_shopping按钮，进入首页可以开始购物
        """
        # 点击空购物车的start_shopping按钮
        self.FE.ele(dweb_cart_ele.ele_cart_start_shopping).click()

    # def check_multi_cart_style_ui(self):
    #     """
    #     # 校验购物车样式-{【109546】 PC购物车-多个购物车样式}
    #     :return:
    #     """
    #     self.page.wait_for_selector(cart_elements.ele_cart_summary)
    #     assert self.FE.ele(cart_elements.ele_cart_summary).is_visible()
    #     assert "title" in self.FE.ele(cart_elements.ele_cart_summary).get_attribute("class")
    #     # 0. 判断第一个购物车是local delivery
    #     assert "Local delivery" == self.FE.ele(cart_elements.ele_cart_summary_local_delivery).text_content()
    #     # 1. 判断subtotal元素存在
    #     assert self.FE.ele(cart_elements.ele_cart_subtatal).is_visible()
    #     sub_total_fee = self.FE.ele(cart_elements.ele_cart_subtatal_fee)
    #     # 2. 判断subtotal值
    #     assert sub_total_fee.is_visible() and "$" in sub_total_fee.text_content()
    #
    #     # 获取所有的items total
    #     items_total = self.FE.eles(cart_elements.ele_cart_items_total)
    #     assert items_total, f"items_total={items_total}"
    #     # 3. 判断items_total中有美元符号存在
    #     for item in items_total:
    #         log.debug("item.text_content===>" + item.text_content())
    #         assert "$" in item.text_content()
    #
    #     # 4. 判断delivery_fee中有美元符号存在或为free
    #     delivery_fee = self.FE.eles(cart_elements.ele_cart_delivery_fee)
    #     for df in delivery_fee:
    #         log.debug("delivery_fee的content===>" + df.text_content())
    #         assert "$" in df.text_content() or 'Free' == df.text_content()
    #
    #     # 5. 判断左侧的购物车
    #     all_cart_div = self.FE.eles(cart_elements.ele_cart_each_cart_div)
    #     assert all_cart_div, f"all_cart_div={all_cart_div}"
    #     for acd in all_cart_div:
    #         all_goods: List[ElementHandle] = acd.query_selector_all("//div[contains(@class, 'GoodsInCart_goods__')]")
    #         assert all_goods, f"all_goods={all_goods}"
    #         for index, ag in enumerate(all_goods):
    #             goods_in_cart_price_action = ag.query_selector(
    #                 u"//div[contains(@class, 'GoodsInCart_goodsActions')]//div[contains(@class, 'GoodsInCart_priceAction')]")
    #             # 校验购物车里"每个商品"的div
    #             assert goods_in_cart_price_action.is_visible()
    #             goods_in_cart_price = ag.query_selector(
    #                 u"//div[contains(@class, 'GoodsInCart_goodsActions')]//div[contains(@class, 'leading-none font-semibold text-center')]")
    #             log.info("each product price in cart===>" + goods_in_cart_price.text_content())
    #             # 校验商品的价格以$开头
    #             assert "$" in goods_in_cart_price.text_content() or "Free" in goods_in_cart_price.text_content()
    #             # 第一个商品有可能是gift商品，没有remove和save_for_later
    #             if index >= 2:
    #                 # 校验remove按钮
    #                 remove = ag.query_selector(u"//div[text()='Remove']")
    #                 # 校验save_for_later
    #                 save_for_later = ag.query_selector(u"//div[text()='Save for later']")
    #                 assert remove.is_visible() and save_for_later.is_visible()
    #             product_name = ag.query_selector(u"//div[contains(@class, 'GoodsInCart_name')]//span").text_content()
    #             log.info("product_name is: " + product_name)
    #             assert len(product_name) > 2
    #
    #     # 7. check底部的recommendations
    #     # 先滚动到Recommendations
    #     while True:
    #         self.page.evaluate('window.scrollBy(0, window.innerHeight)')
    #         self.page.wait_for_timeout(2000)
    #         if self.FE.ele(u"//span[text()='Recommendations']"):
    #             self.FE.ele(u"//span[text()='Recommendations']").scroll_into_view_if_needed()
    #             break
    #
    #     # 7.1 校验标题
    #     assert self.FE.ele(cart_elements.ele_cart_recommendations).text_content() == 'Recommendations'
    #     recommendations_all_goods = self.FE.eles(cart_elements.ele_cart_recommendations_all_goods)
    #     assert recommendations_all_goods, f"购物车推荐商品为0"
    #     # 7.2 校验recommendations下面的商品
    #     for index, i in enumerate(recommendations_all_goods):
    #         # 后面隐藏的商品，继续找加购按钮，找不到，可能因为不可见，需要划动
    #         if index <= 2:
    #             r_add_to_cart_btn = i.query_selector(u"//i[@data-role]")
    #             assert r_add_to_cart_btn.is_enabled()
    #             r_add_to_cart_btn.click()
    #
    def get_normal_cart_amount(self):
        """
        获取当前生鲜购物车金额

        Returns:
            float: 当前购物车金额
        """
        cart_total = self.page.locator(dweb_cart_ele.ele_cart_normal_item_total).text_content()
        normal_cart_amount = float(cart_total.replace("$", "").strip())
        return normal_cart_amount

    def is_empty_cart(self):
        """
        检查购物车是否为空

        Returns:
            bool: 购物车是否为空
        """
        return self.page.locator(dweb_cart_ele.ele_cart_img).is_visible()

    def scroll_to_recommendations(self):
        """
        滚动到购物车底部推荐模块
        """
        self.page.locator(dweb_cart_ele.ele_recommend_module).scroll_into_view_if_needed()
        self.page.wait_for_timeout(1000)

    def get_recommendation_products(self):
        """
        获取推荐商品列表

        Returns:
            list: 推荐商品列表
        """
        return self.page.locator(dweb_cart_ele.ele_recommend_module).get_by_test_id("wid-product-card-container").all()

    def add_recommendation_product(self, index=0):
        """
        加购指定索引的推荐商品

        Args:
            index (int): 推荐商品索引

        Returns:
            bool: 是否成功加购
        """
        products = self.get_recommendation_products()
        for index, item in enumerate(products) :
            item.locator(u"//div[@data-testid='btn-atc-plus']").click()

        # if not products or index >= len(products):
        #     log.info(f"推荐商品列表为空或索引{index}超出范围")
        #     return False
        # products[index].locator(u"//div[@data-testid='btn-atc-plus']").click()
        # self.page.wait_for_timeout(2000)
            log.info(f"加购第{index+1}个推荐商品")
            if index ==0:
                break

        return True

    def add_to_cart_until_target_amount(self, total_amount=None):
        """
        从推荐模块加购商品，直到购物车金额达到目标金额

        Args:
            total_amount (float, optional): 目标金额，如果为None则不限制

        Returns:
            float: 当前购物车金额
        """
        # 滚动到推荐模块
        self.scroll_to_recommendations()

        # 获取推荐商品列表
        recommend_products = self.get_recommendation_products()
        if len(recommend_products) == 0:
            log.info("推荐商品列表为空")
            return self.get_cart_amount()

        log.info(f"找到{len(recommend_products)}个推荐商品")

        # 获取当前购物车金额
        current_amount = self.get_cart_amount()
        log.info(f"当前购物车金额: ${current_amount}")

        # 如果已经达到目标金额，直接返回
        if total_amount is not None and current_amount >= total_amount:
            return current_amount

        # 加购商品直到达到目标金额
        product_index = 0
        while True:
            # 检查是否达到目标金额
            if total_amount is not None and current_amount >= total_amount:
                break

            # 如果推荐商品不够，刷新页面获取更多推荐
            if product_index >= len(recommend_products):
                self.page.reload()
                self.page.wait_for_timeout(3000)
                self.scroll_to_recommendations()
                recommend_products = self.get_recommendation_products()
                if len(recommend_products) == 0:
                    log.info("刷新后推荐商品列表仍为空")
                    break
                product_index = 0
                continue

            # 加购商品
            self.add_recommendation_product(product_index)
            product_index += 1

            # 更新购物车金额
            current_amount = self.get_cart_amount()
            log.info(f"当前购物车金额: ${current_amount}")

        return current_amount

    def remove_normal_cart_item(self, index=0):
        """
        移除生鲜指定索引的购物车商品

        Args:
            index (int): 购物车商品索引

        Returns:
            bool: 是否成功移除
        """

        cart_items = self.page.locator(dweb_cart_ele.ele_cart_normal_card).all()
        if not cart_items or index >= len(cart_items):
            log.info(f"购物车为空或索引{index}超出范围")
            return False
        self.page.locator(dweb_cart_ele.ele_cart_normal_remove).click()
        # cart_items[index].locator("[data-testid='wid-cart-section-remove']").click()
        self.page.wait_for_timeout(2000)
        log.info(f"移除第{index+1}个购物车商品")
        return True

    def remove_non_trade_in_items(self):
        """
        移除所有非换购商品

        Returns:
            int: 移除的商品数量
        """
        cart_items = self.page.locator("[data-testid='wid-cart-section-remove']").all()
        removed_count = 0

        for item in cart_items:
            if not ("trade-in" in item.get_attribute("class") or item.locator("[data-testid='trade-in-badge']").count() > 0):
                item.locator("[data-testid='wid-cart-section-remove']").click()
                self.page.wait_for_timeout(2000)
                removed_count += 1
                log.info(f"移除第{removed_count}个非换购商品")

        return removed_count

    def get_free_shipping_banner_text(self):
        """
        获取免运费banner文案

        Returns:
            str: 免运费banner文案，如果不存在则返回空字符串
        """
        banner = self.page.locator(dweb_cart_ele.ele_cart_banner_normal)
        if banner.is_visible():
            return banner.text_content()
        return ""

    def is_free_shipping_banner_visible(self):
        """
        检查免运费banner是否可见

        Returns:
            bool: 免运费banner是否可见
        """

        banner = self.page.locator(dweb_cart_ele.ele_cart_banner_normal)
        return banner.is_visible()

    def check_trade_in_banner_status(self, cart_amount):
        """
        检查换购banner状态

        Args:
            cart_amount (float): 当前购物车金额

        Returns:
            dict: 包含banner状态信息的字典
        """
        result = {
            "visible": False,
            "text": "",
            "type": ""
        }

        # 检查是否显示免运费banner
        if cart_amount < 35:
            free_shipping_banner = self.page.locator("//div[contains(@class, 'free-shipping-banner')]")
            if free_shipping_banner.is_visible():
                result["visible"] = True
                result["text"] = free_shipping_banner.text_content()
                result["type"] = "free_shipping"
                log.info(f"显示免运费banner: {result['text']}")

        # 检查是否显示换购banner
        elif 35 <= cart_amount < 68:
            trade_in_banner = self.page.locator(dweb_cart_ele.ele_cart_trade_in_normal)
            if trade_in_banner.is_visible():
                result["visible"] = True
                result["text"] = trade_in_banner.text_content()
                result["type"] = "unlock_discounts"
                log.info(f"显示换购入口banner(未解锁): {result['text']}")

        # 检查是否显示已解锁换购banner
        elif cart_amount >= 68:
            trade_in_banner = self.page.locator(dweb_cart_ele.ele_cart_trade_in_normal)
            if trade_in_banner.is_visible():
                result["visible"] = True
                result["text"] = trade_in_banner.text_content()
                result["type"] = "unlocked_discounts"
                log.info(f"显示换购入口banner(已解锁): {result['text']}")

        return result

    def open_trade_in_drawer_and_check(self):
        """
        打开换购抽屉并检查状态

        Returns:
            dict: 包含换购抽屉状态信息的字典
        """
        result = {
            "visible": False,
            "products": [],
            "can_add_to_cart": False
        }

        # 点击换购banner
        trade_in_banner = self.page.get_by_test_id("trade-in-banner")
        if not trade_in_banner.is_visible():
            log.info("换购banner不可见，无法打开换购抽屉")
            return result

        trade_in_banner.click()
        self.page.wait_for_timeout(2000)
        log.info("点击换购banner")

        # 验证换购抽屉弹出
        trade_in_drawer = self.page.get_by_test_id("trade-in-drawer")
        if not trade_in_drawer.is_visible():
            log.info("换购抽屉未弹出")
            return result

        result["visible"] = True
        log.info("换购抽屉弹出成功")

        # 获取换购商品列表
        trade_in_products = self.page.get_by_test_id("trade-in-product").all()
        result["products"] = trade_in_products
        log.info(f"找到{len(trade_in_products)}个换购商品")

        # 检查换购商品是否可加购
        if len(trade_in_products) > 0:
            first_add_btn = trade_in_products[0].get_by_test_id("btn-add-to-cart")
            result["can_add_to_cart"] = not first_add_btn.is_disabled()
            log.info(f"换购商品{'可' if result['can_add_to_cart'] else '不可'}加购")

        return result

    def close_trade_in_drawer(self):
        """
        关闭换购抽屉
        """
        close_btn = self.page.get_by_test_id("btn-close-drawer")
        if close_btn.is_visible():
            close_btn.click()
            self.page.wait_for_timeout(1000)
            log.info("关闭换购抽屉")
    #

    def verify_cart_page_elements(self):
        """
        校验购物车页面元素

        Returns:
            dict: 包含校验结果的字典
        """
        result = {
            'title_verified': False,
            'products_count': 0,
            'success': False
        }

        try:
            # 校验购物车页面标题（Summary区域）
            cart_title = self.page.get_by_test_id("wid-cart-summary-main")
            if cart_title.is_visible(timeout=5000):
                result['title_verified'] = True
                log.info("购物车页面Summary区域校验成功")
            else:
                log.warning("购物车页面Summary区域不可见")

            # 校验购物车中有商品
            cart_items = self.page.locator("//div[@data-testid='wid-cart-section-goods']").all()
            result['products_count'] = len(cart_items)
            log.info(f"购物车中有 {result['products_count']} 个商品")

            # 校验购物车总金额
            total_amount_element = self.page.get_by_test_id("wid-cart-summary-subtotal")
            assert total_amount_element.is_visible(timeout=3000)

            # 整体校验结果
            result['success'] = (
                result['title_verified'] and
                result['products_count'] > 0
            )

            log.info(f"购物车页面校验结果: {result}")
            return result
        except Exception as e:
            log.error(f"购物车页面校验失败: {str(e)}")
            return False

    def click_checkout_button_to_next_page(self):
        """
        点击购物车页面的Checkout按钮进入下一个页面

        Returns:
            bool: 是否成功点击Checkout按钮
        """
        try:
            # 点击Checkout按钮
            checkout_btn = self.page.get_by_test_id("wid-cart-summary-checkout")
            if checkout_btn.is_visible(timeout=5000):
                checkout_btn.click()
                self.page.wait_for_timeout(3000)
                log.info("成功点击Checkout按钮")
                return True
            else:
                log.error("Checkout按钮不可见")
                return False
        except Exception as e:
            log.error(f"点击Checkout按钮失败: {str(e)}")
            return False

    def verify_cart_middle_page_default_state(self):
        """
        验证购物车中间页默认状态

        Returns:
            bool: 验证是否成功
        """
        try:
            # 验证中间页标题
            title = self.page.locator("//span[text()='Select carts to checkout']")
            assert title.is_visible(timeout=5000), "中间页标题不可见"
            log.info("验证中间页标题成功")

            # 验证全选按钮存在
            select_all = self.page.get_by_test_id("wid-cart-select-modal-select-all-btn")
            assert select_all.is_visible(timeout=5000), "全选按钮不可见"
            log.info("验证全选按钮成功")

            # 验证底部提示文本
            tip = self.page.locator("//div[text()='You can select multiple carts for checkout.']")
            assert tip.is_visible(timeout=3000), "底部提示文本不可见"
            log.info("验证底部提示文本成功")

            # 验证结算按钮存在（默认状态下可能是禁用的）
            checkout_btn = self.page.get_by_test_id("wid-cart-select-modal-checkout-btn")
            assert checkout_btn.is_visible(timeout=3000), "中间页结算按钮不可见"
            log.info("验证中间页结算按钮成功")

            log.info("所有中间页默认状态验证成功")
            return True
        except Exception as e:
            log.error(f"验证购物车中间页默认状态失败: {str(e)}")
            return False

    def close_cart_middle_page(self):
        """
        关闭购物车中间页

        Returns:
            bool: 是否成功关闭中间页
        """
        try:
            close_btn = self.page.get_by_test_id("wid-cart-select-modal-close-btn")
            assert close_btn.is_visible(timeout=3000), "关闭按钮不可见"
            close_btn.click()
            self.page.wait_for_timeout(2000)

            # 验证中间页已关闭
            title = self.page.locator("//span[text()='Select carts to checkout']")
            assert not title.is_visible(timeout=3000), "关闭按钮点击后中间页仍然可见"
            log.info("成功关闭中间页")
            return True
        except Exception as e:
            log.error(f"关闭购物车中间页失败: {str(e)}")
            return False