from playwright.sync_api import Page

from src.Dweb.EC.dweb_ele.dweb_home import dweb_home_ele
from src.Dweb.EC.dweb_ele.dweb_cart import dweb_cart_ele
from src.api.zipcode import switch_zipcode
from src.common.commonui import close_advertise_on_home
from src.config.base_config import TEST_URL
from src.Dweb.EC.dweb_pages.dweb_common_page import DWebCommonPage
from src.config.weee.log_help import log


class DWebCartPage(DWebCommonPage):
    """
    这个类主要封装了PC web端对购物车页面的封装
    """
    def __init__(self, page: Page, header, browser_context, zipcode: str = "98011", page_url: str = None):
        """
        构造方法主要包含以下功能：
        1. 进入购物车页面
        2.如果zipcode不是98011，则切换zipcode
        3.调用close_advertise_on_home(self.page)关掉首页的广告
        """
        super().__init__(page, header)
        self.bc = browser_context
        # 直接进入购物车
        self.page.goto(TEST_URL + page_url)
        # 获取顶部语言
        if not self.page.get_by_test_id("wid-language").locator(
                "//span[text()='English'and contains(@class,'Header')]").all():
            # 切换为英文
            pass
        # 获取顶部zipocde
        try:
            self.page.wait_for_timeout(5000)
            page_zipcode = self.page.locator(dweb_home_ele.ele_zipcode).text_content()
            if page_zipcode != zipcode:
                switch_zipcode(headers=self.header)
                self.page.reload()
                self.page.wait_for_timeout(10000)
        except Exception as e:
            log.info("切换zipcode发生异常" + str(e))

        close_advertise_on_home(self.page)

    def start_shopping(self):
        """
        方法包含以下功能：
        1. 点击空购物车的start_shopping按钮，进入首页可以开始购物
        """
        # 点击空购物车的start_shopping按钮
        self.FE.ele(dweb_cart_ele.ele_cart_start_shopping).click()

    # def check_multi_cart_style_ui(self):
    #     """
    #     # 校验购物车样式-{【109546】 PC购物车-多个购物车样式}
    #     :return:
    #     """
    #     self.page.wait_for_selector(cart_elements.ele_cart_summary)
    #     assert self.FE.ele(cart_elements.ele_cart_summary).is_visible()
    #     assert "title" in self.FE.ele(cart_elements.ele_cart_summary).get_attribute("class")
    #     # 0. 判断第一个购物车是local delivery
    #     assert "Local delivery" == self.FE.ele(cart_elements.ele_cart_summary_local_delivery).text_content()
    #     # 1. 判断subtotal元素存在
    #     assert self.FE.ele(cart_elements.ele_cart_subtatal).is_visible()
    #     sub_total_fee = self.FE.ele(cart_elements.ele_cart_subtatal_fee)
    #     # 2. 判断subtotal值
    #     assert sub_total_fee.is_visible() and "$" in sub_total_fee.text_content()
    #
    #     # 获取所有的items total
    #     items_total = self.FE.eles(cart_elements.ele_cart_items_total)
    #     assert items_total, f"items_total={items_total}"
    #     # 3. 判断items_total中有美元符号存在
    #     for item in items_total:
    #         log.debug("item.text_content===>" + item.text_content())
    #         assert "$" in item.text_content()
    #
    #     # 4. 判断delivery_fee中有美元符号存在或为free
    #     delivery_fee = self.FE.eles(cart_elements.ele_cart_delivery_fee)
    #     for df in delivery_fee:
    #         log.debug("delivery_fee的content===>" + df.text_content())
    #         assert "$" in df.text_content() or 'Free' == df.text_content()
    #
    #     # 5. 判断左侧的购物车
    #     all_cart_div = self.FE.eles(cart_elements.ele_cart_each_cart_div)
    #     assert all_cart_div, f"all_cart_div={all_cart_div}"
    #     for acd in all_cart_div:
    #         all_goods: List[ElementHandle] = acd.query_selector_all("//div[contains(@class, 'GoodsInCart_goods__')]")
    #         assert all_goods, f"all_goods={all_goods}"
    #         for index, ag in enumerate(all_goods):
    #             goods_in_cart_price_action = ag.query_selector(
    #                 u"//div[contains(@class, 'GoodsInCart_goodsActions')]//div[contains(@class, 'GoodsInCart_priceAction')]")
    #             # 校验购物车里"每个商品"的div
    #             assert goods_in_cart_price_action.is_visible()
    #             goods_in_cart_price = ag.query_selector(
    #                 u"//div[contains(@class, 'GoodsInCart_goodsActions')]//div[contains(@class, 'leading-none font-semibold text-center')]")
    #             log.info("each product price in cart===>" + goods_in_cart_price.text_content())
    #             # 校验商品的价格以$开头
    #             assert "$" in goods_in_cart_price.text_content() or "Free" in goods_in_cart_price.text_content()
    #             # 第一个商品有可能是gift商品，没有remove和save_for_later
    #             if index >= 2:
    #                 # 校验remove按钮
    #                 remove = ag.query_selector(u"//div[text()='Remove']")
    #                 # 校验save_for_later
    #                 save_for_later = ag.query_selector(u"//div[text()='Save for later']")
    #                 assert remove.is_visible() and save_for_later.is_visible()
    #             product_name = ag.query_selector(u"//div[contains(@class, 'GoodsInCart_name')]//span").text_content()
    #             log.info("product_name is: " + product_name)
    #             assert len(product_name) > 2
    #
    #     # 7. check底部的recommendations
    #     # 先滚动到Recommendations
    #     while True:
    #         self.page.evaluate('window.scrollBy(0, window.innerHeight)')
    #         self.page.wait_for_timeout(2000)
    #         if self.FE.ele(u"//span[text()='Recommendations']"):
    #             self.FE.ele(u"//span[text()='Recommendations']").scroll_into_view_if_needed()
    #             break
    #
    #     # 7.1 校验标题
    #     assert self.FE.ele(cart_elements.ele_cart_recommendations).text_content() == 'Recommendations'
    #     recommendations_all_goods = self.FE.eles(cart_elements.ele_cart_recommendations_all_goods)
    #     assert recommendations_all_goods, f"购物车推荐商品为0"
    #     # 7.2 校验recommendations下面的商品
    #     for index, i in enumerate(recommendations_all_goods):
    #         # 后面隐藏的商品，继续找加购按钮，找不到，可能因为不可见，需要划动
    #         if index <= 2:
    #             r_add_to_cart_btn = i.query_selector(u"//i[@data-role]")
    #             assert r_add_to_cart_btn.is_enabled()
    #             r_add_to_cart_btn.click()
    #
    #

    def verify_cart_page_elements(self):
        """
        校验购物车页面元素

        Returns:
            dict: 包含校验结果的字典
        """
        result = {
            'title_verified': False,
            'products_count': 0,
            'success': False
        }

        try:
            # 校验购物车页面标题（Summary区域）
            cart_title = self.page.get_by_test_id("wid-cart-summary-main")
            if cart_title.is_visible(timeout=5000):
                result['title_verified'] = True
                log.info("购物车页面Summary区域校验成功")
            else:
                log.warning("购物车页面Summary区域不可见")

            # 校验购物车中有商品
            cart_items = self.page.locator("//div[@data-testid='wid-cart-section-goods']").all()
            result['products_count'] = len(cart_items)
            log.info(f"购物车中有 {result['products_count']} 个商品")

            # 校验购物车总金额
            total_amount_element = self.page.get_by_test_id("wid-cart-summary-subtotal")
            assert total_amount_element.is_visible(timeout=3000)

            # 整体校验结果
            result['success'] = (
                result['title_verified'] and
                result['products_count'] > 0
            )

            log.info(f"购物车页面校验结果: {result}")
            return result
        except Exception as e:
            log.error(f"购物车页面校验失败: {str(e)}")
            return False

    def click_checkout_button_to_next_page(self):
        """
        点击购物车页面的Checkout按钮进入下一个页面

        Returns:
            bool: 是否成功点击Checkout按钮
        """
        try:
            # 点击Checkout按钮
            checkout_btn = self.page.get_by_test_id("wid-cart-summary-checkout")
            if checkout_btn.is_visible(timeout=5000):
                checkout_btn.click()
                self.page.wait_for_timeout(3000)
                log.info("成功点击Checkout按钮")
                return True
            else:
                log.error("Checkout按钮不可见")
                return False
        except Exception as e:
            log.error(f"点击Checkout按钮失败: {str(e)}")
            return False

    def verify_cart_middle_page_default_state(self):
        """
        验证购物车中间页默认状态

        Returns:
            bool: 验证是否成功
        """
        try:
            # 验证中间页标题
            title = self.page.locator("//span[text()='Select carts to checkout']")
            assert title.is_visible(timeout=5000), "中间页标题不可见"
            log.info("验证中间页标题成功")

            # 验证全选按钮存在
            select_all = self.page.get_by_test_id("wid-cart-select-modal-select-all-btn")
            assert select_all.is_visible(timeout=5000), "全选按钮不可见"
            log.info("验证全选按钮成功")

            # 验证底部提示文本
            tip = self.page.locator("//div[text()='You can select multiple carts for checkout.']")
            assert tip.is_visible(timeout=3000), "底部提示文本不可见"
            log.info("验证底部提示文本成功")

            # 验证结算按钮存在（默认状态下可能是禁用的）
            checkout_btn = self.page.get_by_test_id("wid-cart-select-modal-checkout-btn")
            assert checkout_btn.is_visible(timeout=3000), "中间页结算按钮不可见"
            log.info("验证中间页结算按钮成功")

            log.info("所有中间页默认状态验证成功")
            return True
        except Exception as e:
            log.error(f"验证购物车中间页默认状态失败: {str(e)}")
            return False

    def close_cart_middle_page(self):
        """
        关闭购物车中间页

        Returns:
            bool: 是否成功关闭中间页
        """
        try:
            close_btn = self.page.get_by_test_id("wid-cart-select-modal-close-btn")
            assert close_btn.is_visible(timeout=3000), "关闭按钮不可见"
            close_btn.click()
            self.page.wait_for_timeout(2000)

            # 验证中间页已关闭
            title = self.page.locator("//span[text()='Select carts to checkout']")
            assert not title.is_visible(timeout=3000), "关闭按钮点击后中间页仍然可见"
            log.info("成功关闭中间页")
            return True
        except Exception as e:
            log.error(f"关闭购物车中间页失败: {str(e)}")
            return False