import pytest
from playwright.sync_api import Page

from src.Dweb.EC.dweb_pages.dweb_common_page import DWebCommonPage
from src.common.commonui import close_advertise_on_home
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log


class DWebAddressPage(DWebCommonPage):
    """
    这个类主要封装了PC web端对地址页面的操作
    """
    def __init__(self, page: Page, header, browser_context):
        """
        构造方法
        """
        super().__init__(page, header)
        self.bc = browser_context
        self.page.goto(TEST_URL)
        self.page.wait_for_timeout(10000)
        close_advertise_on_home(self.page)

    def add_new_address_from_home(self):
        """
        从首页添加新地址
        """
        # 点击首页地址按钮
        self.page.get_by_test_id("wid-modal-zip-code").click()
        self.page.wait_for_timeout(2000)

        # 点击新增地址按钮
        self.page.get_by_test_id("wid-btn-add-address").click()
        self.page.wait_for_timeout(3000)

        # 填写地址表单
        self._fill_address_form()

        # # 验证地址是否添加成功
        # self.page.get_by_test_id("wid-zipcode").click()
        # self.page.wait_for_timeout(2000)
        #
        # # 检查地址簿中是否有新增的地址
        # assert self.page.get_by_test_id("wid-address-name").filter(has_text="Test Automation").is_visible(), "首页地址簿中未找到新增的地址"

        # 清理：删除测试地址
        self.page.goto(TEST_URL + "/account/settings")
        self.page.wait_for_timeout(4000)
        self._delete_test_address()
        assert self.page.locator("div[data-name='me_delivery_address']").is_visible()

        log.info("从首页添加新地址成功")

    def add_new_address_from_account(self):
        """
        从账户页面添加新地址
        """
        # 进入账户页面
        self.page.goto(TEST_URL + "/account/settings")
        self.page.wait_for_timeout(3000)

        # 点击地址管理
        self.page.locator("div[data-name='me_delivery_address']").click()
        self.page.wait_for_timeout(3000)

        # 点击新增地址按钮
        self.page.locator("//span[text()='Add a new address']").click()
        self.page.wait_for_timeout(2000)

        # 填写地址表单
        self._fill_address_form()

        # 清理：删除测试地址
        self._delete_test_address()

        log.info("从账户页面添加新地址成功")

    def add_new_address_from_checkout(self):
        """
        从结算页面添加新地址
        """
        # 添加商品到购物车（因为空购物车无法进入结算页面）
        # 点击首页上的第一个商品的加购按钮
        try:
            # 尝试点击首页上的第一个加购按钮
            self.page.get_by_test_id("btn-atc-plus").first.click()
            self.page.wait_for_timeout(2000)
            log.info("成功添加商品到购物车")
        except Exception as e:
            raise Exception("首页没有找到加购按钮")

        # 点击购物车图标进入购物车页面
        self.page.get_by_test_id("wid-mini-cart").click()
        self.page.wait_for_timeout(3000)
        self.page.locator("//a[@aria-label='Weee! Logo']").hover()
        self.page.wait_for_timeout(3000)

        # 点击结算按钮
        self.page.get_by_test_id("wid-cart-summary-checkout").click()
        self.page.wait_for_timeout(5000)

        # 偶现弹出alcohol agreement，还要处理alcohol agreement

        # 如果有多购物车，则要处理多购物车
        if self.page.get_by_test_id("wid-cart-select-modal-select-all-btn").all():
            self.page.get_by_test_id("wid-cart-select-modal-select-all-btn").click()
            self.page.wait_for_timeout(3000)
            self.page.get_by_test_id("wid-cart-select-modal-checkout-btn").click()
            self.page.wait_for_timeout(5000)

        # 如果有upsell,处理upsell
        if self.page.get_by_test_id("wid-upsell-continue-to-checkout").all():
            self.page.get_by_test_id("wid-upsell-continue-to-checkout").click()
            self.page.wait_for_timeout(3000)

        # 点击新建地址按钮
        # 如果已有地址
        if self.page.get_by_test_id("wid-checkout-address-info-btn").all():
            self.page.get_by_test_id("wid-checkout-address-info-btn").locator("//span").click()
            self.page.wait_for_timeout(2000)
        # 如果没有地址
        else:
            self.page.locator("//i[contains(@class, 'cursor-pointer')]").first.click()
            self.page.wait_for_timeout(2000)
            if self.page.get_by_test_id("wid-checkout-address-info-btn").all():
                self.page.get_by_test_id("wid-checkout-address-info-btn").locator("//span").click()
                self.page.wait_for_timeout(5000)
            else:
                pytest.skip(reason="没有找到新建地址按钮")


        # 填写地址表单
        self._fill_address_form()

        # 清理：删除测试地址（需要进入账户页面删除）
        self.page.goto(TEST_URL + "/account/settings")
        self.page.wait_for_timeout(3000)
        self._delete_test_address()

        log.info("从结算页面添加新地址成功")

    def add_new_address_from_order_detail(self, order_id):
        """
        从订单详情页添加新地址
        """
        # 进入订单详情页
        self.page.goto(TEST_URL + f"/account/orders/{order_id}")
        self.page.wait_for_timeout(3000)

        # 点击更改地址
        self.page.get_by_test_id("btn-change-address").click()
        self.page.wait_for_timeout(2000)

        # 点击新增地址按钮
        self.page.get_by_test_id("btn-add-new-address").click()
        self.page.wait_for_timeout(2000)

        # 填写地址表单
        self._fill_address_form()

        # 验证地址是否添加成功
        assert self.page.get_by_test_id("wid-address-name").filter(has_text="Test Automation").is_visible(), "订单详情页地址簿中未找到新增的地址"

        # 清理：删除测试地址（需要进入账户页面删除）
        self.page.goto(TEST_URL + "/account/settings")
        self.page.wait_for_timeout(3000)
        self._delete_test_address()

        log.info("从订单详情页添加新地址成功")

    def _fill_address_form(self):
        """
        填写地址表单
        """
        # 这个地方开发没加好，所有的input输入框的testid都是wid-input-zipcode
        # 输入姓名
        self.page.locator("//label[text()='First name']//following-sibling::input").fill("Bill")
        self.page.locator("//label[text()='Last name']//following-sibling::input").fill("Smitch")
        self.page.get_by_test_id("")

        # 输入电话
        self.page.locator("//label[text()='Phone number']//following-sibling::input").fill("**********")

        # 输入街道地址
        self.page.locator("#rc_select_0").fill("15006 104th Ave NE")
        self.page.locator("//label[text()='City']//following-sibling::input").fill("Bothell")
        self.page.locator("#rc_select_1").fill("WA")
        self.page.locator("//div[text()='WA']").click()
        self.page.locator("//label[text()='Zip code']//following-sibling::input").fill("98011")

        # 输入备注
        self.page.locator("//textarea").fill("PC UI自动化测试")
        # 点击保存按钮
        self.page.locator("//button[text()='Save']").click()
        self.page.wait_for_timeout(3000)
        if self.page.locator("//button[text()='Not now']").all():
            self.page.locator("//button[text()='Not now']").click()
            self.page.wait_for_timeout(2000)

    def _delete_test_address(self):
        """
        删除测试地址
        """
        # 找到address book div
        self.page.locator("div[data-name='me_delivery_address']").click()
        self.page.wait_for_timeout(3000)
        # 进入地址列表
        test_address = self.page.locator("//div[@data-id]//div[contains(text(), 'Bill')]//..//following-sibling::div[@data-testid]")
        if test_address.is_visible():
            log.info("找到测试地址")
            # 点击edit按钮
            test_address.click()
            self.page.wait_for_timeout(1000)

            # 确认删除
            self.page.locator("//button[text()='Delete the address']").click()
            self.page.wait_for_timeout(2000)

            log.info("测试地址删除成功")

