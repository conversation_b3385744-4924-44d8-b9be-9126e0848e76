"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   dweb_page_checkout.py
@Description    :  PC结算页面对象
@CreateTime     :  2025/6/10 14:30
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/6/10 14:30
"""
import allure
from playwright.sync_api import Page, expect

from src.Dweb.EC.dweb_ele.dweb_category import dweb_category_ele
from src.Dweb.EC.dweb_ele.dweb_checkout import dweb_checkout_ele
from src.Dweb.EC.dweb_ele.dweb_home import dweb_home_ele
from src.Dweb.EC.dweb_pages.dweb_common_page import DWebCommonPage
from src.api.zipcode import switch_zipcode
from src.common.commonui import close_advertise_on_home, scroll_one_page_until
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log


class DWebCheckoutPage(DWebCommonPage):
    """结算页面类"""

    def __init__(self, page: Page, header, browser_context, zipcode: str = "98011", page_url: str = None):
        """
        构造方法主要包含以下功能：
        1. 进入结算页面
        2. 如果zipcode不是98011，则切换zipcode
        3. 调用close_advertise_on_home(self.page)关掉首页的广告
        """
        super().__init__(page, header)
        self.bc = browser_context
        # 直接进入结算页
        if page_url:
            self.page.goto(TEST_URL + page_url)
        else:
            self.page.goto(TEST_URL + "/order/checkout?cart_domain=grocery")
        
        # 获取顶部语言
        if not self.page.get_by_test_id("wid-language").locator(
                "//span[text()='English'and contains(@class,'Header')]").all():
            # 切换为英文
            pass
        
        # 获取顶部zipocde
        # page_zipcode = self.page.locator(dweb_home_ele.ele_zipcode).text_content()
        # if page_zipcode != zipcode:
        #     switch_zipcode(headers=self.header)
        #     self.page.reload()
        #
        # self.page.wait_for_timeout(5000)
        # close_advertise_on_home(self.page)

    def verify_checkout_title(self):
        """验证结算页标题"""
        with allure.step("验证会员等级显示"):
            try:
                # 获取会员等级信息
                member_level = self.page.get_by_test_id('wid-checkout-rewards-header')
                level_text = member_level.text_content()

                # 根据不同等级验证对应的图标和文案
                if "Bronze" in level_text.upper():
                    # p.get_by_test_id('wid-checkout-rewards-header')
                    expect(self.FE.ele("//div[@data-testid='btn-atc-plus']").locator('img')).to_be_visible()
                    expect(self.page.locator("text=Bronze Rewards member")).to_be_visible()
                elif "Silver" in level_text.upper():
                    expect(self.page.get_by_test_id('wid-checkout-rewards-header').locator('img')).to_be_visible()
                    expect(self.page.locator("text=Silver Rewards member")).to_be_visible()
                elif "Gold" in level_text.upper():
                    expect(self.page.get_by_test_id('wid-checkout-rewards-header').locator('img')).to_be_visible()
                    expect(self.page.locator("text=Gold Rewards member")).to_be_visible()
                log.info(f"会员等级验证成功: {level_text}")
            except Exception as e:
                log.error(f"验证会员等级显示失败: {str(e)}")
                raise
        # title = self.page.get_by_test_id("wid-checkout-title")
        # assert title.is_visible(), "结算页标题不可见"
        # title_text = title.text_content()
        # log.info(f"结算页标题: {title_text}")
        # assert "Checkout" in title_text, "结算页标题不正确"
        # return title_text

    def verify_shipping_section(self):
        """验证配送信息区域"""
        shipping_section =self.FE.ele(dweb_checkout_ele.ele_delivery_info)
        assert shipping_section.is_visible(), "配送信息区域不可见"
        log.info("配送信息区域可见")
        # 验证配送地址
        address_info = self.page.get_by_test_id("wid-checkout-address-info")
        assert address_info.is_visible(), "配送地址区域不可见"
        address_text = address_info.text_content()
        log.info(f"配送地址: {address_text}")
        self.page.wait_for_timeout(2000)
        # 验证review order 区域存在
        # 滚动到指定位置
        # scroll_one_page_until(self.page,'wid-checkout-review-order-0'))
        # delivery_time = self.page.get_by_test_id("wid-checkout-review-order-0")
        # assert delivery_time.is_visible(), "review order区域不存在"
        # delivery_time_text = delivery_time.text_content()
        # log.info(f"配送时间: {delivery_time_text}")
        #
        # return {
        #     "address": address_text,
        #     "delivery_time": delivery_time_text
        # }

    def verify_payment_section(self):
        """验证支付信息区域"""
        with allure.step("验证支付信息区域"):
            try:
                # 验证支付信息区域存在
                payment_section = self.FE.ele(dweb_checkout_ele.ele_payment_section)
                assert payment_section.is_visible(), "支付信息区域不可见"
                log.info("支付信息区域可见")
                
                # 检查是否存在积分模块
                rewards_section = self.page.get_by_test_id("wid-checkout-rewards-section")
                has_rewards = rewards_section.is_visible()
                
                if has_rewards:
                    # 验证积分模块元素
                    log.info("积分模块存在，验证积分相关元素")
                    
                    # 验证积分标题
                    rewards_header = self.page.get_by_test_id("wid-checkout-rewards-header")
                    assert rewards_header.is_visible(), "积分标题不可见"
                    rewards_header_text = rewards_header.text_content()
                    log.info(f"积分标题: {rewards_header_text}")
                    
                    # 验证积分数量
                    rewards_points = self.page.get_by_test_id("wid-checkout-rewards-points")
                    if rewards_points.is_visible():
                        points_text = rewards_points.text_content()
                        log.info(f"积分数量: {points_text}")
                    
                    # 验证积分应用按钮
                    rewards_apply_btn = self.page.get_by_test_id("wid-checkout-rewards-apply")
                    if rewards_apply_btn.is_visible():
                        log.info("积分应用按钮可见")
                    
                    # 验证已应用积分文本（如果已应用）
                    rewards_applied = self.page.get_by_test_id("wid-checkout-rewards-applied")
                    if rewards_applied.is_visible():
                        applied_text = rewards_applied.text_content()
                        log.info(f"已应用积分文本: {applied_text}")
                else:
                    log.info("积分模块不存在")
                
                # 无论是否有积分模块，都验证支付方式
                payment_method = self.FE.ele(dweb_checkout_ele.ele_payment_method)
                assert payment_method.is_visible(), "支付方式区域不可见"
                payment_method_text = payment_method.text_content()
                log.info(f"支付方式: {payment_method_text}")
                
                # 验证支付卡信息（如果有）
                payment_card = self.page.locator(dweb_checkout_ele.ele_payment_card)
                if payment_card.is_visible():
                    card_text = payment_card.text_content()
                    log.info(f"支付卡信息: {card_text}")
                    
                    # 验证卡号
                    card_number = self.page.locator(dweb_checkout_ele.ele_payment_card_number)
                    if card_number.is_visible():
                        number_text = card_number.text_content()
                        log.info(f"卡号: {number_text}")
                    
                    # 验证到期日期
                    card_expiry = self.page.locator(dweb_checkout_ele.ele_payment_card_expiry)
                    if card_expiry.is_visible():
                        expiry_text = card_expiry.text_content()
                        log.info(f"到期日期: {expiry_text}")
                
                return {
                    "payment_section_visible": True,
                    "has_rewards": has_rewards,
                    "payment_method_text": payment_method_text
                }
            except Exception as e:
                log.error(f"验证支付信息区域失败: {str(e)}")
                raise

    def verify_order_summary(self):
        """验证订单摘要信息"""
        # 验证订单摘要区域
        summary_section = self.page.get_by_test_id("wid-checkout-order-summary")
        assert summary_section.is_visible(), "订单摘要区域不可见"
        log.info("订单摘要区域可见")
        
        # 验证小计
        subtotal = self.page.get_by_test_id("wid-checkout-subtotal")
        assert subtotal.is_visible(), "小计信息不可见"
        subtotal_text = subtotal.text_content()
        log.info(f"订单小计: {subtotal_text}")
        assert "$" in subtotal_text, "小计金额格式不正确"
        
        # 验证配送费
        delivery_fee = self.page.get_by_test_id("wid-checkout-delivery-fee")
        assert delivery_fee.is_visible(), "配送费信息不可见"
        delivery_fee_text = delivery_fee.text_content()
        log.info(f"配送费: {delivery_fee_text}")
        
        # 验证税费
        tax = self.page.get_by_test_id("wid-checkout-tax")
        assert tax.is_visible(), "税费信息不可见"
        tax_text = tax.text_content()
        log.info(f"税费: {tax_text}")
        
        # 验证总计
        total = self.page.get_by_test_id("wid-checkout-total")
        assert total.is_visible(), "总计信息不可见"
        total_text = total.text_content()
        log.info(f"订单总计: {total_text}")
        assert "$" in total_text, "总计金额格式不正确"
        
        return {
            "subtotal": subtotal_text,
            "delivery_fee": delivery_fee_text,
            "tax": tax_text,
            "total": total_text
        }

    def verify_cart_items(self):
        """验证购物车商品列表"""
        # 验证商品列表区域
        items_section = self.page.get_by_test_id("wid-checkout-items")
        assert items_section.is_visible(), "商品列表区域不可见"
        log.info("商品列表区域可见")
        
        # 获取所有商品
        items = self.page.get_by_test_id("wid-checkout-item").all()
        assert len(items) > 0, "商品列表为空"
        log.info(f"商品列表中有 {len(items)} 个商品")
        
        items_info = []
        for i, item in enumerate(items):
            # 获取商品名称
            item_name = item.get_by_test_id("wid-checkout-item-name")
            assert item_name.is_visible(), f"第 {i+1} 个商品名称不可见"
            name_text = item_name.text_content()
            
            # 获取商品价格
            item_price = item.get_by_test_id("wid-checkout-item-price")
            assert item_price.is_visible(), f"第 {i+1} 个商品价格不可见"
            price_text = item_price.text_content()
            assert "$" in price_text, f"第 {i+1} 个商品价格格式不正确"
            
            # 获取商品数量
            item_quantity = item.get_by_test_id("wid-checkout-item-quantity")
            assert item_quantity.is_visible(), f"第 {i+1} 个商品数量不可见"
            quantity_text = item_quantity.text_content()
            
            log.info(f"商品 {i+1}: 名称={name_text}, 价格={price_text}, 数量={quantity_text}")
            items_info.append({
                "name": name_text,
                "price": price_text,
                "quantity": quantity_text
            })
        
        return items_info

    def verify_place_order_button(self):
        """验证下单按钮"""
        place_order_btn = self.FE.ele(dweb_checkout_ele.ele_place_order)
        assert place_order_btn.is_visible(), "下单按钮不可见"
        assert place_order_btn.is_enabled(), "下单按钮未启用"
        button_text = place_order_btn.text_content()
        log.info(f"下单按钮文本: {button_text}")
        assert "Place order" in button_text, "下单按钮文本不正确"
        return place_order_btn