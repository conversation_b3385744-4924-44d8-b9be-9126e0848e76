{"name": "帮助中心-热点问题文章查看流程验证", "status": "passed", "description": "\n        测试帮助中心热点问题文章查看流程\n        步骤：\n        1. 访问帮助中心页面\n        2. 点击热点问题分类\n        3. 点击如何修改订单地址文章\n        4. 验证文章页面标题\n        5. 点击有用按钮\n        6. 验证按钮变为灰色\n        ", "start": *************, "stop": *************, "uuid": "85a41ade-593b-4145-b596-49ca7144ab15", "historyId": "ee170903f91d2b1333f4dd82979ab823", "testCaseId": "ee170903f91d2b1333f4dd82979ab823", "fullName": "src.Dweb.EC.testcases.dweb_account.dweb_help.test_107901_dweb_help_like_ui_ux.TestHelpCenter#test_help_center_hot_issues", "labels": [{"name": "story", "value": "帮助中心-文章查看流程"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pchelp"}, {"name": "tag", "value": "transaction"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_account.dweb_help"}, {"name": "suite", "value": "test_107901_dweb_help_like_ui_ux"}, {"name": "subSuite", "value": "TestHelpCenter"}, {"name": "host", "value": "SHLAP10453"}, {"name": "thread", "value": "26312-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_account.dweb_help.test_107901_dweb_help_like_ui_ux"}]}