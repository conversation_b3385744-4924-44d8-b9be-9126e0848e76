import allure
import pytest

from playwright.sync_api import Page

from src.Dweb.EC.dweb_ele import dweb_common_ele
from src.Dweb.EC.dweb_ele.dweb_account.dweb_order import dweb_order_list_ele, dweb_buy_again_ele
from src.Dweb.EC.dweb_pages.dweb_page_account.dweb_page_order.dweb_page_order_list import DWebOrderListPage
from src.config.weee.log_help import log


@allure.story("【111625】 订单列表-再来一单功能验证")
class TestDWebMyOrderBuyAgainUIUX:
    pytestmark = [pytest.mark.dweb_porder, pytest.mark.dweb_regression, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("【111625】 订单列表-再来一单功能验证")
    def test_111625_dWeb_my_order_buy_again_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        【111625】 订单列表-再来一单功能验证
        测试步骤：
        1、进入订单列表页面，点击切换到已取消tab下
        2、找到订单列表下再来一单按钮，右侧会撤拉出商品选择页面
        3、商品选择页面，默认勾选全选按钮，勾选掉某个商品，全选去掉，加入购物车按钮置灰，点击全选商品全部选中，这里如果选中几个商品，加入购物车按钮上就会显示几个
        4、点击加入购物车按钮，自动回到订单列表页面
        """
        p: Page = page.get("page")
        c = page.get("context")

        # 1. 进入订单列表页面
        order_page = DWebOrderListPage(p, pc_autotest_header, browser_context=c, page_url="/account/my_orders")
        p.wait_for_timeout(3000)
        log.info("成功进入订单列表页面")

        # 切换到已取消tab
        cancelled_tab = p.get_by_test_id(dweb_order_list_ele.order_cancelled_tab_ele)
        assert cancelled_tab.is_visible(), "未找到已取消tab"
        # 切换到已取消 tab
        cancelled_tab.click()
        p.wait_for_timeout(2000)
        # 验证已切换到已取消 tab
        assert "filter_status=4" in p.url, "未切换到已取消tab"
        # 2. 检查是否存在订单
        empty_state = p.get_by_test_id(dweb_order_list_ele.empty_image_ele)
        if empty_state.is_visible():
            # 已取消tab下没有订单
            # 2. 如果没有订单，点击start shopping按钮
            start_shopping_btn = p.get_by_test_id(dweb_order_list_ele.start_shopping_btn_ele)
            assert start_shopping_btn.is_visible(), "未找到start shopping按钮"
            assert p.get_by_test_id(dweb_order_list_ele.empty_title_ele)
            start_shopping_btn.click()
            p.wait_for_timeout(3000)
            # 判断进入首页
            assert p.get_by_test_id("wid-view-more").is_visible(), "未成功跳转到首页"
        else:
            # 获取订单列表
            order_R_items = p.get_by_test_id(dweb_order_list_ele.order_list_R_card_ele).all()
            order_S_items = p.get_by_test_id(dweb_order_list_ele.order_list_S_card_ele).all()

            if len(order_R_items) == 0 and len(order_S_items) == 0:
                log.warning("已发货tab下没有normal+seller 订单，无法继续测试")
                pytest.skip("已发货tab下没有normal+seller 订单，跳过测试")
            elif len(order_R_items) > 0:
                for index, item_R in enumerate(order_R_items):
                    # 1、点击订单列表上的再来一单按钮，拉起再来一单pop
                    log.info("步骤1：点击订单列表上的再来一单按钮")
                    buy_again_btn = item_R.get_by_test_id(dweb_order_list_ele.order_buy_again_btn)
                    assert buy_again_btn.is_visible(), "再来一单按钮不可见"
                    buy_again_btn.click()
                    p.wait_for_timeout(3000)

                    # 2、验证 buy_again_page 元素存在
                    log.info("步骤2：验证再来一单页面元素存在")
                    buy_again_page = p.get_by_test_id(dweb_buy_again_ele.buy_again_page_title)
                    assert buy_again_page.is_visible(), "再来一单页面未正确显示"
                    # 验证有效商品区域存在
                    buy_again_available = p.get_by_test_id(dweb_buy_again_ele.buy_again_available)
                    assert buy_again_available.is_visible(), "再来一单内容区域不可见"
                    # 验证有效商品列表存在
                    buy_again_product_list = buy_again_available.get_by_test_id(
                        dweb_buy_again_ele.buy_again_available_item).all()
                    if len(buy_again_product_list) > 0:
                        log.info(f"有效商品列表存在{len(buy_again_product_list)}个商品")
                        for item in buy_again_product_list:
                            assert item.get_by_test_id(
                                dweb_buy_again_ele.buy_again_item_checkbox).is_visible(), "有效商品列表不可见"
                            assert item.get_by_test_id(
                                dweb_buy_again_ele.buy_again_available_item_content).is_checked(), "有效商品列表未被选中"
                            assert item.get_by_test_id(
                                "wid-account-buy-again-product-item-name").is_visible(), "有效商品列表标题不可见"
                            assert item.get_by_test_id(
                                "wid-account-buy-again-product-item-price").is_visible(), "有效商品列表价格不可见"
                            assert item.get_by_test_id("wid-account-buy-again-product-item-image").is_visible(), "有效商品列表图片不可见"
                            item_class = item.get_attribute("class")
                            # 断言商品是默认选中状态
                            assert "buy-again_selected" in item_class, "有效商品列表未被选中"

                    # 验证无效商品区域存在
                    buy_again_unavailable = p.get_by_test_id(dweb_buy_again_ele.buy_again_unavailable)
                    assert buy_again_unavailable.is_visible(), "无效商品区域不可见"
                    # 验证无效商品列表存在
                    buy_again_unavailable_product_list = buy_again_unavailable.get_by_test_id(
                        dweb_buy_again_ele.buy_again_available_product).all()
                    if len(buy_again_unavailable_product_list) > 0:
                        log.info(f"无效商品列表存在{len(buy_again_unavailable_product_list)}个商品")
                        for item in buy_again_unavailable_product_list:
                            assert item.get_by_test_id(
                                dweb_buy_again_ele.buy_again_item_checkbox).is_visible(), "无效商品列表不可见"
                            assert not item.get_by_test_id(
                                dweb_buy_again_ele.buy_again_available_item_content).is_checked(), "无效商品列表被选中"
                            assert item.get_by_test_id(
                                "wid-order-buy-again-product-item-content-title").is_visible(), "无效商品列表标题不可见"

                    # 3、点击切换日期
                    if p.get_by_test_id(dweb_buy_again_ele.buy_again_page_info).is_visible():
                        p.get_by_test_id(dweb_buy_again_ele.buy_again_page_info).click()
                        p.wait_for_timeout(2000)
                        # 断言进入切换日期pop页面
                        assert p.get_by_test_id(dweb_common_ele.ele_delivery_date_popup).is_visible(), "切换日期弹窗未显示"
                        # 点击切换日期
                        delivery_data = p.get_by_test_id(dweb_common_ele.ele_delivery_date).all()
                        # 如果日期大于2个，只选第一个
                        if len(delivery_data) >= 2:
                            delivery_data[1].click()
                            p.wait_for_timeout(2000)
                            # 断言回到再来一单选择商品页面
                            assert p.get_by_test_id(dweb_buy_again_ele.buy_again_page).is_visible(), "未成功回到再来一单选择商品页面"
                        else:
                            # 如果只有一个日期，直接点击
                            delivery_data[0].click()
                            p.wait_for_timeout(2000)
                            # 断言回到再来一单选择商品页面
                            assert p.get_by_test_id(dweb_buy_again_ele.buy_again_page).is_visible(), "未成功回到再来一单选择商品页面"
                    # 验证选中状态-------------------------------------
                    # 4、select all 按钮全选状态
                    log.info("步骤3：验证select all按钮并执行全选操作")
                    select_all_btn = p.get_by_test_id(dweb_buy_again_ele.buy_again_select_all)
                    assert select_all_btn.is_visible(), "全选按钮不可见"
                    # 验证按钮默认选中状态
                    # 获取class 属性
                    select_all_class = select_all_btn.get_attribute('class')
                    # 断言 class 中包含 "BuyAgain_selected"
                    assert "buy-again_selected" in select_all_class, "The 'Select all' checkbox is not in selected state"
                    # 断言第一个商品的checkbox 也是选中状态
                    assert "BuyAgain_selected" in buy_again_available.get_by_test_id(
                        dweb_buy_again_ele.buy_again_available_product[0]).get_attribute('class')

                    # 5. 点击全选按钮（取消全选）
                    select_all_btn.click()
                    p.wait_for_timeout(2000)
                    # 获取元素的 class 属性
                    select_all_class = select_all_btn.get_attribute('class')
                    # 断言父元素的 class 中包含 "BuyAgain_selected"
                    assert "buy-again_disabledSelect" in select_all_class, "The 'Select all' checkbox is not in selected state"
                    # 断言第一个商品的checkbox 是未选中状态
                    assert "BuyAgain_selected" in buy_again_available.get_by_test_id(
                        dweb_buy_again_ele.buy_again_available_product[0]).get_attribute('class')
                    # 断言 加购按钮不能点击

                    # 6、再次点击全选按钮
                    log.info("步骤4：取消全选操作")
                    select_all_btn.click()  # 再次点击全选按钮取消全选
                    p.wait_for_timeout(2000)

                    # 验证取消全选状态 - 检查商品复选框是否都被取消选中
                    # 待补充
                    # 点击加购按钮
                    p.get_by_test_id(dweb_buy_again_ele.buy_again_add_cart_btn).click()
                    p.wait_for_timeout(2000)
                    # 断言回到订单列表页面
                    assert p.get_by_test_id(dweb_order_list_ele.order_list_R_card_ele).is_visible(), "未成功回到订单列表页面"
                    # 只测试第一个订单，避免重复测试
                    break
            elif len(order_S_items) > 0:
                for index, item_S in enumerate(order_S_items):
                    # 1、点击订单列表上的再来一单按钮，拉起再来一单pop
                    log.info("步骤1：点击订单列表上的再来一单按钮")
                    buy_again_btn = item_S.get_by_test_id(dweb_order_list_ele.order_buy_again_btn)
                    assert buy_again_btn.is_visible(), "再来一单按钮不可见"
                    buy_again_btn.click()
                    p.wait_for_timeout(3000)

                    # 2、验证 buy_again_page 元素存在
                    log.info("步骤2：验证再来一单页面元素存在")
                    buy_again_page = p.get_by_test_id(dweb_buy_again_ele.buy_again_page_title)
                    assert buy_again_page.is_visible(), "再来一单页面未正确显示"
                    # 验证有效商品区域存在
                    buy_again_available = p.get_by_test_id(dweb_buy_again_ele.buy_again_available)
                    assert buy_again_available.is_visible(), "再来一单内容区域不可见"
                    # 验证有效商品列表存在
                    buy_again_product_list = buy_again_available.get_by_test_id(
                        dweb_buy_again_ele.buy_again_available_item).all()
                    if len(buy_again_product_list) > 0:
                        log.info(f"有效商品列表存在{len(buy_again_product_list)}个商品")
                        for item in buy_again_product_list:
                            assert item.get_by_test_id(
                                dweb_buy_again_ele.buy_again_item_checkbox).is_visible(), "有效商品列表不可见"
                            assert item.get_by_test_id(
                                dweb_buy_again_ele.buy_again_available_item_content).is_checked(), "有效商品列表未被选中"
                            assert item.get_by_test_id(
                                "wid-account-buy-again-product-item-name").is_visible(), "有效商品列表标题不可见"
                            assert item.get_by_test_id(
                                "wid-account-buy-again-product-item-price").is_visible(), "有效商品列表价格不可见"
                            assert item.get_by_test_id(
                                "wid-account-buy-again-product-item-image").is_visible(), "有效商品列表图片不可见"
                            item_class = item.get_attribute("class")
                            # 断言商品是默认选中状态
                            assert "buy-again_selected" in item_class, "有效商品列表未被选中"

                    # 验证无效商品区域存在
                    buy_again_unavailable = p.get_by_test_id(dweb_buy_again_ele.buy_again_unavailable)
                    assert buy_again_unavailable.is_visible(), "无效商品区域不可见"
                    # 验证无效商品列表存在
                    buy_again_unavailable_product_list = buy_again_unavailable.get_by_test_id(
                        dweb_buy_again_ele.buy_again_available_product).all()
                    if len(buy_again_unavailable_product_list) > 0:
                        log.info(f"无效商品列表存在{len(buy_again_unavailable_product_list)}个商品")
                        for item in buy_again_unavailable_product_list:
                            assert item.get_by_test_id(
                                dweb_buy_again_ele.buy_again_item_checkbox).is_visible(), "无效商品列表不可见"
                            assert not item.get_by_test_id(
                                dweb_buy_again_ele.buy_again_available_item_content).is_checked(), "无效商品列表被选中"
                            assert item.get_by_test_id(
                                "wid-order-buy-again-product-item-content-title").is_visible(), "无效商品列表标题不可见"

                    # 3、点击切换日期
                    assert not p.get_by_test_id(dweb_buy_again_ele.buy_again_page_info).is_visible()

                    # 验证选中状态-------------------------------------
                    # 4、select all 按钮全选状态
                    log.info("步骤3：验证select all按钮并执行全选操作")
                    select_all_btn = p.get_by_test_id(dweb_buy_again_ele.buy_again_select_all)
                    assert select_all_btn.is_visible(), "全选按钮不可见"
                    # 验证按钮默认选中状态
                    # 获取class 属性
                    select_all_class = select_all_btn.get_attribute('class')
                    # 断言 class 中包含 "BuyAgain_selected"
                    assert "buy-again_selected" in select_all_class, "The 'Select all' checkbox is not in selected state"
                    # 断言第一个商品的checkbox 也是选中状态
                    assert "BuyAgain_selected" in buy_again_available.get_by_test_id(
                        dweb_buy_again_ele.buy_again_available_product[0]).get_attribute('class')

                    # 5. 点击全选按钮（取消全选）
                    select_all_btn.click()
                    p.wait_for_timeout(2000)
                    # 获取元素的 class 属性
                    select_all_class = select_all_btn.get_attribute('class')
                    # 断言父元素的 class 中包含 "BuyAgain_selected"
                    assert "buy-again_disabledSelect" in select_all_class, "The 'Select all' checkbox is not in selected state"
                    # 断言第一个商品的checkbox 是未选中状态
                    assert "BuyAgain_selected" in buy_again_available.get_by_test_id(
                        dweb_buy_again_ele.buy_again_available_product[0]).get_attribute('class')
                    # 断言 加购按钮不能点击

                    # 6、再次点击全选按钮
                    log.info("步骤4：取消全选操作")
                    select_all_btn.click()  # 再次点击全选按钮取消全选
                    p.wait_for_timeout(2000)

                    # 验证取消全选状态 - 检查商品复选框是否都被取消选中
                    # 待补充
                    # 点击加购按钮
                    p.get_by_test_id(dweb_buy_again_ele.buy_again_add_cart_btn).click()
                    p.wait_for_timeout(2000)
                    # 断言回到订单列表页面
                    assert p.get_by_test_id(dweb_order_list_ele.order_list_R_card_ele).is_visible(), "未成功回到订单列表页面"
                    # 只测试第一个订单，避免重复测试
                    break