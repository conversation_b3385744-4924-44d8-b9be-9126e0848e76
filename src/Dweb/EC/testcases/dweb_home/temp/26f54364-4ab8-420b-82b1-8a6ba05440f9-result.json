{"name": "explore store的页面验证", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.", "trace": "self = <src.Dweb.EC.testcases.dweb_home.test_003_page_home.TestAtHome object at 0x000001CD4AF8B990>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppData\\Lo...aywright\\chromium-1091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...VLXwLzX3KgcQbJyer5qqkfBaZpauWtUCHIpxo-__mXVNjXV85gKKgq2ioVVjIcbuQjFbyZnhC22mnjtjZOrif3y9P8P2RhzclZ3dpc_VQ0ypxRl0', ...}\n\n    @pytest.fixture(scope='class')\n    def setup(self, page: dict, pc_autotest_header):\n>       hp = DWebHomePage(page[\"page\"], pc_autotest_header, page.get(\"context\"))\n\ntest_003_page_home.py:19: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n..\\..\\dweb_pages\\dweb_page_home\\dweb_page_home.py:35: in __init__\n    zipcode = self.page.locator(\"//div[@id='changeZipCode']/div[position()=1]\").text_content()\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:18050: in text_content\n    self._sync(self._impl_obj.text_content(timeout=timeout))\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:607: in text_content\n    return await self._frame.text_content(\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:609: in text_content\n    return await self._channel.send(\"textContent\", locals_to_params(locals()))\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x000001CD4D567ED0>\nmethod = 'textContent'\nparams = {'selector': \"//div[@id='changeZipCode']/div[position()=1]\", 'strict': True}\nreturn_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.\n\n..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: TimeoutError"}, "description": "\n        MS用例号: 110756: 切换zipcode逻辑\n        ", "start": 1752485710253, "stop": 1752485710253, "uuid": "19fbddf2-58aa-4b94-8b0d-a725e35bfd95", "historyId": "02f07f94ce5c9f7eeb43a1dd24832f75", "testCaseId": "02f07f94ce5c9f7eeb43a1dd24832f75", "fullName": "src.Dweb.EC.testcases.dweb_home.test_003_page_home.TestAtHome#test_each_store_page_check", "labels": [{"name": "story", "value": "首页搜索by category"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_home"}, {"name": "suite", "value": "test_003_page_home"}, {"name": "subSuite", "value": "TestAtHome"}, {"name": "host", "value": "SHLAP10453"}, {"name": "thread", "value": "1820-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_home.test_003_page_home"}]}