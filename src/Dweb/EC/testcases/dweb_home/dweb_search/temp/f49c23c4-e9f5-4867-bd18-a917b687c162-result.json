{"name": "首页按热词搜索", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.TargetClosedError: Target page, context or browser has been closed", "trace": "self = <src.Dweb.EC.testcases.dweb_home.dweb_search.test_002_home_search_page.TestSearchAtHome object at 0x0000018812E59FD0>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppData\\Lo...1091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/account/my_orders'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...r_WV12jMFMQZ_-iPCoCwz9tdflk98HNzR0lf6r7s48yNcmqijglimlVzS483Hxd4vl2LG-35DuJVP9WlIEOPJALg9XIQmIp0fkynX349lf2cZEro', ...}\nlogin_trace = None\n\n    @allure.title(\"首页按热词搜索\")\n    def test_search_by_hotkey(self, page: dict, pc_autotest_header, login_trace):\n        hsp = HomeSearchPage(page[\"page\"], pc_autotest_header)\n>       hsp.hot_key_search()\n\ntest_002_home_search_page.py:19: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n..\\..\\..\\dweb_pages\\dweb_page_home\\dweb_page_home_search_by_category_at_home.py:72: in hot_key_search\n    self.page.get_by_test_id(\"wid-main-search-no-fouce\").click()\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:15784: in click\n    self._sync(\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:159: in click\n    return await self._frame.click(self._selector, strict=True, **params)\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:484: in click\n    await self._channel.send(\"click\", locals_to_params(locals()))\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x0000018813277F10>\nmethod = 'click'\nparams = {'selector': 'internal:testid=[data-testid=\"wid-main-search-no-fouce\"s]', 'strict': True}\nreturn_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.TargetClosedError: Target page, context or browser has been closed\n\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: TargetClosedError"}, "start": 1752485915597, "stop": 1752485947910, "uuid": "39c3d470-c7cd-4a91-89b3-1f45a642808a", "historyId": "b710eb9520a251b5f89d0e5b99c80326", "testCaseId": "b710eb9520a251b5f89d0e5b99c80326", "fullName": "src.Dweb.EC.testcases.dweb_home.dweb_search.test_002_home_search_page.TestSearchAtHome#test_search_by_hotkey", "labels": [{"name": "story", "value": "首页搜索"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_home.dweb_search"}, {"name": "suite", "value": "test_002_home_search_page"}, {"name": "subSuite", "value": "TestSearchAtHome"}, {"name": "host", "value": "SHLAP10453"}, {"name": "thread", "value": "29456-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_home.dweb_search.test_002_home_search_page"}]}