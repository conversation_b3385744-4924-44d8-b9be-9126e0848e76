{"name": "test_zipcode_switching_for_popup", "status": "broken", "statusDetails": {"message": "_pytest.fixtures.FixtureLookupError: ('popup_responses', <FixtureRequest for <Function test_zipcode_switching_for_popup>>)", "trace": "file D:\\python ui\\qa-ui-dmweb\\src\\Dweb\\EC\\testcases\\dweb_home\\dweb_popup\\test_108263_home_popup.py, line 115\n      def test_zipcode_switching_for_popup(self, page, popup_responses, current_zipcode=None):\nE       fixture 'popup_responses' not found\n>       available fixtures: __pytest_repeat_step_number, _session_faker, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, doctest_namespace, faker, login_page, login_trace, monkeypatch, not_login_page, not_login_trace, page, pc_anony_header, pc_autotest_header, playwright, porder, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, user_setup\n>       use 'pytest --fixtures [testpath]' for help on them.\n\nD:\\python ui\\qa-ui-dmweb\\src\\Dweb\\EC\\testcases\\dweb_home\\dweb_popup\\test_108263_home_popup.py:115"}, "description": "测试zipcode切换寻找popup", "start": 1752809077604, "stop": 1752809077604, "uuid": "0f5599ba-8a29-43b8-80b5-04ae80c12c5c", "historyId": "927889b796a620d319d2c00924e7f6b5", "testCaseId": "927889b796a620d319d2c00924e7f6b5", "fullName": "src.Dweb.EC.testcases.dweb_home.dweb_popup.test_108263_home_popup.TestHomeActivityPopup#test_zipcode_switching_for_popup", "labels": [{"name": "story", "value": "首页活动弹窗测试"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_home.dweb_popup"}, {"name": "suite", "value": "test_108263_home_popup"}, {"name": "subSuite", "value": "TestHomeActivityPopup"}, {"name": "host", "value": "SHLAP10453"}, {"name": "thread", "value": "31128-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_home.dweb_popup.test_108263_home_popup"}]}