{"name": "首页活动弹窗功能完整流程测试", "status": "skipped", "statusDetails": {"message": "Skipped: 切换了所有zipcode ['99348', '46311'] 都未发现popup，跳过此测试用例。原因：可能之前已经弹过了或当前时间段没有活动", "trace": "('D:\\\\python ui\\\\qa-ui-dmweb\\\\src\\\\Dweb\\\\EC\\\\testcases\\\\dweb_home\\\\dweb_popup\\\\test_108263_home_popup.py', 196, \"Skipped: 切换了所有zipcode ['99348', '46311'] 都未发现popup，跳过此测试用例。原因：可能之前已经弹过了或当前时间段没有活动\")"}, "description": "\n        测试首页活动弹窗完整流程\n        1. 打开首页检查popup接口调用\n        2. 如果popup_exist=true，测试弹窗点击跳转\n        3. 如果popup_exist=false，切换zipcode测试\n        4. 依次测试99348和46311两个zipcode\n        5. 连续两个zipcode都没有popup则跳过\n        ", "start": 1752817460157, "stop": 1752817485241, "uuid": "4ddfea14-eeeb-45e3-aafb-a2f0743b6a66", "historyId": "dccdb67e3ab28c8aff794ca8253cf8f7", "testCaseId": "dccdb67e3ab28c8aff794ca8253cf8f7", "fullName": "src.Dweb.EC.testcases.dweb_home.dweb_popup.test_108263_home_popup.TestHomeActivityPopup#test_108263_home_activity_popup_flow", "labels": [{"name": "story", "value": "首页活动弹窗测试"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_home.dweb_popup"}, {"name": "suite", "value": "test_108263_home_popup"}, {"name": "subSuite", "value": "TestHomeActivityPopup"}, {"name": "host", "value": "SHLAP10453"}, {"name": "thread", "value": "24172-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_home.dweb_popup.test_108263_home_popup"}]}