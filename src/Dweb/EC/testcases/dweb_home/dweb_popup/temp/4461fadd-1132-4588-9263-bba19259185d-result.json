{"name": "test_108263_home_popup", "status": "broken", "statusDetails": {"message": "_pytest.fixtures.FixtureLookupError: ('dweb_page_home', <FixtureRequest for <Function test_108263_home_popup>>)", "trace": "file D:\\python ui\\qa-ui-dmweb\\src\\Dweb\\EC\\testcases\\dweb_home\\dweb_popup\\test_108263_home_popup.py, line 8\n      def test_108263_home_popup(self, dweb_page_home: DWebHomePage):\nE       fixture 'dweb_page_home' not found\n>       available fixtures: __pytest_repeat_step_number, _session_faker, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, doctest_namespace, faker, login_page, login_trace, monkeypatch, not_login_page, not_login_trace, page, pc_anony_header, pc_autotest_header, playwright, porder, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, user_setup\n>       use 'pytest --fixtures [testpath]' for help on them.\n\nD:\\python ui\\qa-ui-dmweb\\src\\Dweb\\EC\\testcases\\dweb_home\\dweb_popup\\test_108263_home_popup.py:8"}, "description": "\n        测试首页活动弹窗功能\n        1. 访问首页获取活动弹窗接口\n        2. 如果弹出popup先关闭\n        3. 切换zipcode触发popup\n        4. 点击popup跳转到活动页\n        ", "start": 1752228238330, "stop": 1752228238330, "uuid": "a776a93b-90a2-4114-bc2f-e3fa46065b60", "historyId": "a3f147943677cd94b76317a2fa461277", "testCaseId": "a3f147943677cd94b76317a2fa461277", "fullName": "src.Dweb.EC.testcases.dweb_home.dweb_popup.test_108263_home_popup.TestHomePopup#test_108263_home_popup", "labels": [{"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_home.dweb_popup"}, {"name": "suite", "value": "test_108263_home_popup"}, {"name": "subSuite", "value": "TestHomePopup"}, {"name": "host", "value": "SHLAP10453"}, {"name": "thread", "value": "19040-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_home.dweb_popup.test_108263_home_popup"}]}