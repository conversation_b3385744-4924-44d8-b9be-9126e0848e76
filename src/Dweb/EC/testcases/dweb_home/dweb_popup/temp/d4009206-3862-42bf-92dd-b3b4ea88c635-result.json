{"name": "zipcode切换功能独立测试", "status": "passed", "description": "\n        独立测试zipcode切换功能\n        ", "start": 1752806467843, "stop": 1752806494356, "uuid": "fb4bc1bb-9e86-4b24-9740-820ead0cb128", "historyId": "e6c186515fc8d8a9089c55fcf5599e52", "testCaseId": "e6c186515fc8d8a9089c55fcf5599e52", "fullName": "src.Dweb.EC.testcases.dweb_home.dweb_popup.test_108263_home_popup.TestHomeActivityPopup#test_zipcode_switch_functionality", "labels": [{"name": "story", "value": "首页活动弹窗测试"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_home.dweb_popup"}, {"name": "suite", "value": "test_108263_home_popup"}, {"name": "subSuite", "value": "TestHomeActivityPopup"}, {"name": "host", "value": "SHLAP10453"}, {"name": "thread", "value": "15100-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_home.dweb_popup.test_108263_home_popup"}]}