{"name": "test_popup_click_navigation", "status": "broken", "statusDetails": {"message": "AttributeError: 'dict' object has no attribute 'get_by_test_id'", "trace": "self = <src.Dweb.EC.testcases.dweb_home.dweb_popup.test_108263_home_popup.TestHomeActivityPopup object at 0x0000024B4A38BAD0>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppData\\Lo...win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/cms/page/activity/sale-Crazy8'>}\n\n    def test_popup_click_navigation(self, page):\n        \"\"\"测试popup点击跳转功能\"\"\"\n        try:\n            # 检查popup是否显示\n>           popup_link = page.get_by_test_id(\"wid-activity-image-content-image-link\")\nE           AttributeError: 'dict' object has no attribute 'get_by_test_id'\n\ntest_108263_home_popup.py:83: AttributeError"}, "description": "测试popup点击跳转功能", "start": 1752806927108, "stop": 1752806927108, "uuid": "7c06a3a8-3378-46bd-948b-aa810eb9138a", "historyId": "f14c31b6bed0e40eb3830a9141da2ac8", "testCaseId": "f14c31b6bed0e40eb3830a9141da2ac8", "fullName": "src.Dweb.EC.testcases.dweb_home.dweb_popup.test_108263_home_popup.TestHomeActivityPopup#test_popup_click_navigation", "labels": [{"name": "story", "value": "首页活动弹窗测试"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_home.dweb_popup"}, {"name": "suite", "value": "test_108263_home_popup"}, {"name": "subSuite", "value": "TestHomeActivityPopup"}, {"name": "host", "value": "SHLAP10453"}, {"name": "thread", "value": "23532-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_home.dweb_popup.test_108263_home_popup"}]}