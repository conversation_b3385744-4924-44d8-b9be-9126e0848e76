{"name": "首页活动弹窗功能测试", "status": "broken", "statusDetails": {"message": "AttributeError: 'TestHomePopup' object has no attribute 'check_activity_popup'", "trace": "self = <src.Dweb.EC.testcases.dweb_home.dweb_popup.test_108263_home_popup.TestHomePopup object at 0x00000252D5909CD0>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppData\\Lo...91\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en?referral_id=9664862'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...7DTKr9LcW2iKf61fMYZQfmt2R7ut4U8q4yr2gr2y8-fHu7Clk8L6GwmcLp1uNrQhLjusIhYfIA6OEImsAnCLq37aZKiRt7h4TNLVsvpaawBPnQLk', ...}\nlogin_trace = None\n\n    @allure.title(\"首页活动弹窗功能测试\")\n    def test_108263_home_popup(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        测试首页活动弹窗功能\n        1. 访问首页获取活动弹窗接口\n        2. 如果弹出popup先关闭\n        3. 切换zipcode触发popup\n        4. 点击popup跳转到活动页\n        \"\"\"\n        test_page = page[\"page\"]\n        log.info(\"开始测试首页活动弹窗\")\n    \n        # 1. 访问首页\n        test_page.goto(\"https://www.sayweee.com/en?referral_id=9664862\")\n        test_page.wait_for_timeout(8000)\n        test_page.wait_for_load_state(\"networkidle\", timeout=60000)\n    \n        # 关闭可能存在的广告弹窗\n        try:\n            ad_close = test_page.locator(\"//div[@class='ant-modal-body']//img[@alt='Close']\").first\n            if ad_close.is_visible(timeout=3000):\n                ad_close.click()\n                test_page.wait_for_timeout(2000)\n        except:\n            log.info(\"没有广告弹窗需要关闭\")\n    \n        # 2. 检查是否有活动弹窗\n>       popup_exists = self.check_activity_popup(test_page)\nE       AttributeError: 'TestHomePopup' object has no attribute 'check_activity_popup'\n\ntest_108263_home_popup.py:38: AttributeError"}, "description": "\n        测试首页活动弹窗功能\n        1. 访问首页获取活动弹窗接口\n        2. 如果弹出popup先关闭\n        3. 切换zipcode触发popup\n        4. 点击popup跳转到活动页\n        ", "start": 1752810604410, "stop": 1752810616631, "uuid": "3304a0ce-ecd4-4758-b9fc-138650a3dccd", "historyId": "a3f147943677cd94b76317a2fa461277", "testCaseId": "a3f147943677cd94b76317a2fa461277", "fullName": "src.Dweb.EC.testcases.dweb_home.dweb_popup.test_108263_home_popup.TestHomePopup#test_108263_home_popup", "labels": [{"name": "story", "value": "首页活动弹窗"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_home.dweb_popup"}, {"name": "suite", "value": "test_108263_home_popup"}, {"name": "subSuite", "value": "TestHomePopup"}, {"name": "host", "value": "SHLAP10453"}, {"name": "thread", "value": "18400-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_home.dweb_popup.test_108263_home_popup"}]}