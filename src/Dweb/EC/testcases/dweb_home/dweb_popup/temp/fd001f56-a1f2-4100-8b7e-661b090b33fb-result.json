{"name": "首页活动弹窗功能完整流程测试", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.", "trace": "self = <src.Dweb.EC.testcases.dweb_home.dweb_popup.test_108263_home_popup.TestHomeActivityPopup object at 0x000002357BF8B050>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppData\\Lo...aywright\\chromium-1091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer..._wZz7uGwnYmqENPSqJOzzM-NQecDC8abqgVW7bHjpoEwKenpaH-9vIgwqxEpbrG_k0WFCzfbzdoIb2y6Su_2HvRmfsftic0N7jb3Yd-uGt3V6hak', ...}\n\n    @pytest.fixture(scope='class')\n    def setup(self, page: dict, pc_autotest_header):\n>       hp = DWebHomePage(page[\"page\"], pc_autotest_header, page.get(\"context\"))\n\ntest_108263_home_popup.py:13: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n..\\..\\..\\dweb_pages\\dweb_page_home\\dweb_page_home.py:35: in __init__\n    zipcode = self.page.locator(\"//div[@id='changeZipCode']/div[position()=1]\").text_content()\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:18050: in text_content\n    self._sync(self._impl_obj.text_content(timeout=timeout))\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:607: in text_content\n    return await self._frame.text_content(\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:609: in text_content\n    return await self._channel.send(\"textContent\", locals_to_params(locals()))\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x00000235018CA110>\nmethod = 'textContent'\nparams = {'selector': \"//div[@id='changeZipCode']/div[position()=1]\", 'strict': True}\nreturn_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.\n\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: TimeoutError"}, "description": "\n        测试首页活动弹窗完整流程\n        1. 打开首页检查popup接口调用\n        2. 如果popup_exist=true，测试弹窗点击跳转\n        3. 如果popup_exist=false，切换zipcode测试\n        4. 依次测试99348和46311两个zipcode\n        5. 连续两个zipcode都没有popup则跳过\n        ", "start": 1752805984467, "stop": 1752805984467, "uuid": "f2d125c6-0dd4-4799-bcae-ed9977a2a051", "historyId": "dccdb67e3ab28c8aff794ca8253cf8f7", "testCaseId": "dccdb67e3ab28c8aff794ca8253cf8f7", "fullName": "src.Dweb.EC.testcases.dweb_home.dweb_popup.test_108263_home_popup.TestHomeActivityPopup#test_108263_home_activity_popup_flow", "labels": [{"name": "story", "value": "首页活动弹窗测试"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_home.dweb_popup"}, {"name": "suite", "value": "test_108263_home_popup"}, {"name": "subSuite", "value": "TestHomeActivityPopup"}, {"name": "host", "value": "SHLAP10453"}, {"name": "thread", "value": "28204-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_home.dweb_popup.test_108263_home_popup"}]}