{"name": "test_zipcode_switching_for_popup", "status": "skipped", "statusDetails": {"message": "Skipped: 切换了所有zipcode都未发现popup，跳过此测试用例。原因：可能之前已经弹过了或当前时间段没有活动", "trace": "('D:\\\\python ui\\\\qa-ui-dmweb\\\\src\\\\Dweb\\\\EC\\\\testcases\\\\dweb_home\\\\dweb_popup\\\\test_108263_home_popup.py', 166, 'Skipped: 切换了所有zipcode都未发现popup，跳过此测试用例。原因：可能之前已经弹过了或当前时间段没有活动')"}, "description": "测试zipcode切换寻找popup", "start": 1752810076249, "stop": 1752810076260, "uuid": "9ef60fa1-b777-4359-ba6f-67bf14021f87", "historyId": "927889b796a620d319d2c00924e7f6b5", "testCaseId": "927889b796a620d319d2c00924e7f6b5", "fullName": "src.Dweb.EC.testcases.dweb_home.dweb_popup.test_108263_home_popup.TestHomeActivityPopup#test_zipcode_switching_for_popup", "labels": [{"name": "story", "value": "首页活动弹窗测试"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_home.dweb_popup"}, {"name": "suite", "value": "test_108263_home_popup"}, {"name": "subSuite", "value": "TestHomeActivityPopup"}, {"name": "host", "value": "SHLAP10453"}, {"name": "thread", "value": "16480-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_home.dweb_popup.test_108263_home_popup"}]}