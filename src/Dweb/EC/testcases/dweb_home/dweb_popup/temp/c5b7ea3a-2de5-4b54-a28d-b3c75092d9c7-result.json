{"name": "首页活动弹窗功能完整流程测试", "status": "passed", "description": "\n        测试首页活动弹窗完整流程\n        1. 打开首页检查popup接口调用\n        2. 如果popup_exist=true，测试弹窗点击跳转\n        3. 如果popup_exist=false，切换zipcode测试\n        4. 依次测试99348和46311两个zipcode\n        5. 连续两个zipcode都没有popup则跳过\n        ", "start": 1752817300177, "stop": 1752817331644, "uuid": "3dddddbf-af06-4c92-bfd5-4b2ee2a3c335", "testCaseId": "dccdb67e3ab28c8aff794ca8253cf8f7", "fullName": "src.Dweb.EC.testcases.dweb_home.dweb_popup.test_108263_home_popup.TestHomeActivityPopup#test_108263_home_activity_popup_flow"}