{"name": "test_popup_click_navigation", "status": "failed", "statusDetails": {"message": "AssertionError: popup未显示\nassert False\n +  where False = <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/cms/page/activity/sale-Crazy8'> selector='internal:testid=[data-testid=\"wid-activity-image-content-image-link\"s]'>>(timeout=5000)\n +    where <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/cms/page/activity/sale-Crazy8'> selector='internal:testid=[data-testid=\"wid-activity-image-content-image-link\"s]'>> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/cms/page/activity/sale-Crazy8'> selector='internal:testid=[data-testid=\"wid-activity-image-content-image-link\"s]'>.is_visible", "trace": "self = <src.Dweb.EC.testcases.dweb_home.dweb_popup.test_108263_home_popup.TestHomeActivityPopup object at 0x000002841F0BBC10>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppData\\Lo...win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/cms/page/activity/sale-Crazy8'>}\n\n    def test_popup_click_navigation(self, page):\n        \"\"\"测试popup点击跳转功能\"\"\"\n        try:\n            # 确保 page 是 Page 对象，而不是字典\n            if isinstance(page, dict):\n                actual_page = page[\"page\"]\n            else:\n                actual_page = page\n    \n            # 检查popup是否显示\n            popup_link = actual_page.get_by_test_id(\"wid-activity-image-content-image-link\")\n>           assert popup_link.is_visible(timeout=5000), \"popup未显示\"\nE           AssertionError: popup未显示\nE           assert False\nE            +  where False = <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/cms/page/activity/sale-Crazy8'> selector='internal:testid=[data-testid=\"wid-activity-image-content-image-link\"s]'>>(timeout=5000)\nE            +    where <bound method Locator.is_visible of <Locator frame=<Frame name= url='https://www.sayweee.com/en/cms/page/activity/sale-Crazy8'> selector='internal:testid=[data-testid=\"wid-activity-image-content-image-link\"s]'>> = <Locator frame=<Frame name= url='https://www.sayweee.com/en/cms/page/activity/sale-Crazy8'> selector='internal:testid=[data-testid=\"wid-activity-image-content-image-link\"s]'>.is_visible\n\ntest_108263_home_popup.py:94: AssertionError"}, "description": "测试popup点击跳转功能", "start": 1752808337147, "stop": 1752808337214, "uuid": "bdc027a1-9ab1-4ea5-b76c-37c7b15311d6", "historyId": "f14c31b6bed0e40eb3830a9141da2ac8", "testCaseId": "f14c31b6bed0e40eb3830a9141da2ac8", "fullName": "src.Dweb.EC.testcases.dweb_home.dweb_popup.test_108263_home_popup.TestHomeActivityPopup#test_popup_click_navigation", "labels": [{"name": "story", "value": "首页活动弹窗测试"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_home.dweb_popup"}, {"name": "suite", "value": "test_108263_home_popup"}, {"name": "subSuite", "value": "TestHomeActivityPopup"}, {"name": "host", "value": "SHLAP10453"}, {"name": "thread", "value": "12000-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_home.dweb_popup.test_108263_home_popup"}]}