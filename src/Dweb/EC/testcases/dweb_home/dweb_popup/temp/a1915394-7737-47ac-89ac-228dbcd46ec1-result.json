{"name": "test_popup_click_navigation", "status": "broken", "statusDetails": {"message": "_pytest.fixtures.FixtureLookupError: ('self', <FixtureRequest for <Function test_popup_click_navigation>>)", "trace": "file D:\\python ui\\qa-ui-dmweb\\src\\Dweb\\EC\\testcases\\dweb_home\\dweb_popup\\test_108263_home_popup.py, line 4\n  def test_popup_click_navigation(self, page):\nE       fixture 'self' not found\n>       available fixtures: __pytest_repeat_step_number, _session_faker, cache, capfd, capfdbinary, caplog, capsys, capsysbinary, doctest_namespace, faker, login_page, login_trace, monkeypatch, not_login_page, not_login_trace, page, pc_anony_header, pc_autotest_header, playwright, porder, pytestconfig, record_property, record_testsuite_property, record_xml_attribute, recwarn, tmp_path, tmp_path_factory, tmpdir, tmpdir_factory, user_setup\n>       use 'pytest --fixtures [testpath]' for help on them.\n\nD:\\python ui\\qa-ui-dmweb\\src\\Dweb\\EC\\testcases\\dweb_home\\dweb_popup\\test_108263_home_popup.py:4"}, "description": "测试popup点击跳转功能", "start": 1752807137456, "stop": 1752807137456, "uuid": "24a52127-dcca-4bbc-b02c-f3ecd339cade", "historyId": "45ac20e54ae365f28d80c972d41a5187", "testCaseId": "45ac20e54ae365f28d80c972d41a5187", "fullName": "src.Dweb.EC.testcases.dweb_home.dweb_popup.test_108263_home_popup#test_popup_click_navigation", "labels": [{"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_home.dweb_popup"}, {"name": "suite", "value": "test_108263_home_popup"}, {"name": "host", "value": "SHLAP10453"}, {"name": "thread", "value": "25024-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_home.dweb_popup.test_108263_home_popup"}]}