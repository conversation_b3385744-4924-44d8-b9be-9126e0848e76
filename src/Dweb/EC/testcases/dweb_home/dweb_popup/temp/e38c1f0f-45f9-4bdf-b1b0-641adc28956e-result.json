{"name": "zipcode切换功能独立测试", "status": "passed", "description": "\n        独立测试zipcode切换功能\n        ", "start": 1752811802084, "stop": 1752811845508, "uuid": "02fc9ede-c476-4fc0-9678-c0b6976b8e8d", "historyId": "e6c186515fc8d8a9089c55fcf5599e52", "testCaseId": "e6c186515fc8d8a9089c55fcf5599e52", "fullName": "src.Dweb.EC.testcases.dweb_home.dweb_popup.test_108263_home_popup.TestHomeActivityPopup#test_zipcode_switch_functionality", "labels": [{"name": "story", "value": "首页活动弹窗测试"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_home.dweb_popup"}, {"name": "suite", "value": "test_108263_home_popup"}, {"name": "subSuite", "value": "TestHomeActivityPopup"}, {"name": "host", "value": "SHLAP10453"}, {"name": "thread", "value": "20508-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_home.dweb_popup.test_108263_home_popup"}]}