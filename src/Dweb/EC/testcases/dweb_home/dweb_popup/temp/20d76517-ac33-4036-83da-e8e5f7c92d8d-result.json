{"name": "popup接口监听测试", "status": "passed", "description": "\n        独立测试popup接口监听功能\n        ", "start": 1752811850022, "stop": 1752811859526, "uuid": "e638f843-184f-40f6-9d44-d576b956dbdf", "historyId": "4d2a846ae6ec56947b41d6290eb585f7", "testCaseId": "4d2a846ae6ec56947b41d6290eb585f7", "fullName": "src.Dweb.EC.testcases.dweb_home.dweb_popup.test_108263_home_popup.TestHomeActivityPopup#test_popup_api_monitoring", "labels": [{"name": "story", "value": "首页活动弹窗测试"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_home.dweb_popup"}, {"name": "suite", "value": "test_108263_home_popup"}, {"name": "subSuite", "value": "TestHomeActivityPopup"}, {"name": "host", "value": "SHLAP10453"}, {"name": "thread", "value": "20508-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_home.dweb_popup.test_108263_home_popup"}]}