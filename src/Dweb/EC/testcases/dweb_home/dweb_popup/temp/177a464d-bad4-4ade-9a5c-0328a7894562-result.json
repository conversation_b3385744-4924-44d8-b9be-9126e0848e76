{"name": "首页活动弹窗功能完整流程测试", "status": "broken", "statusDetails": {"message": "AttributeError: 'TestHomeActivityPopup' object has no attribute 'close_advertisement_popup'", "trace": "self = <src.Dweb.EC.testcases.dweb_home.dweb_popup.test_108263_home_popup.TestHomeActivityPopup object at 0x000001DCF077B810>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppData\\Lo...aywright\\chromium-1091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...E1XpHXb9jdGhENEaYiO6WDNGXH33RZgye8AwNZ73WWdjQZrkxKJy0I5qh9wSFKttlPK3gukE-Rk7lo7qXl2C4veboZc40bemGDHPYcSHHVFRLkXI', ...}\nlogin_trace = None\n\n    @allure.title(\"首页活动弹窗功能完整流程测试\")\n    def test_108263_home_activity_popup_flow(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        测试首页活动弹窗完整流程\n        1. 打开首页检查popup接口调用\n        2. 如果popup_exist=true，测试弹窗点击跳转\n        3. 如果popup_exist=false，切换zipcode测试\n        4. 依次测试99348和46311两个zipcode\n        5. 连续两个zipcode都没有popup则跳过\n        \"\"\"\n        test_page = page[\"page\"]\n        log.info(\"开始测试首页活动弹窗完整流程\")\n    \n        # 监听popup接口调用\n        popup_responses = []\n    \n        def handle_response(response):\n            if \"/ec/activity/popup/page\" in response.url and \"page=page_home\" in response.url:\n                try:\n                    response_data = response.json()\n                    popup_responses.append({\n                        'url': response.url,\n                        'data': response_data,\n                        'popup_exist': response_data.get('object', {}).get('popup_exist', False)\n                    })\n                    log.info(\n                        f\"捕获到popup接口: popup_exist={response_data.get('object', {}).get('popup_exist', False)}\")\n                except Exception as e:\n                    log.warning(f\"无法解析popup接口响应: {str(e)}\")\n    \n        test_page.on(\"response\", handle_response)\n    \n        # 1. 打开首页\n        test_page.goto(\"https://www.sayweee.com/en\")\n        test_page.wait_for_timeout(8000)\n        test_page.wait_for_load_state(\"networkidle\", timeout=30000)\n    \n        # 关闭可能的广告弹窗\n>       self.close_advertisement_popup(test_page)\nE       AttributeError: 'TestHomeActivityPopup' object has no attribute 'close_advertisement_popup'\n\ntest_108263_home_popup.py:390: AttributeError"}, "description": "\n        测试首页活动弹窗完整流程\n        1. 打开首页检查popup接口调用\n        2. 如果popup_exist=true，测试弹窗点击跳转\n        3. 如果popup_exist=false，切换zipcode测试\n        4. 依次测试99348和46311两个zipcode\n        5. 连续两个zipcode都没有popup则跳过\n        ", "start": 1752816211807, "stop": 1752816225390, "uuid": "e2c4c943-f2e0-4077-b189-abcdbc88ce35", "historyId": "dccdb67e3ab28c8aff794ca8253cf8f7", "testCaseId": "dccdb67e3ab28c8aff794ca8253cf8f7", "fullName": "src.Dweb.EC.testcases.dweb_home.dweb_popup.test_108263_home_popup.TestHomeActivityPopup#test_108263_home_activity_popup_flow", "labels": [{"name": "story", "value": "首页活动弹窗测试"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_home.dweb_popup"}, {"name": "suite", "value": "test_108263_home_popup"}, {"name": "subSuite", "value": "TestHomeActivityPopup"}, {"name": "host", "value": "SHLAP10453"}, {"name": "thread", "value": "25636-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_home.dweb_popup.test_108263_home_popup"}]}