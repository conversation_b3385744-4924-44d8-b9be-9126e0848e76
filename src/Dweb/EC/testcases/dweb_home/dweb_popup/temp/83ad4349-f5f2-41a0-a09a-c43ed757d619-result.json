{"name": "首页活动弹窗功能测试", "status": "skipped", "statusDetails": {"message": "Skipped: 未能在任何zipcode下触发活动弹窗，跳过测试", "trace": "('D:\\\\python ui\\\\qa-ui-dmweb\\\\src\\\\Dweb\\\\EC\\\\testcases\\\\dweb_home\\\\dweb_popup\\\\test_108263_home_popup.py', 111, 'Skipped: 未能在任何zipcode下触发活动弹窗，跳过测试')"}, "description": "\n        测试首页活动弹窗功能\n        1. 访问首页获取活动弹窗接口\n        2. 如果弹出popup先关闭\n        3. 切换zipcode触发popup\n        4. 点击popup跳转到活动页\n        ", "start": 1752484804901, "stop": 1752484820680, "uuid": "9656a4d4-60e1-4328-b188-8e22681366e5", "historyId": "a3f147943677cd94b76317a2fa461277", "testCaseId": "a3f147943677cd94b76317a2fa461277", "fullName": "src.Dweb.EC.testcases.dweb_home.dweb_popup.test_108263_home_popup.TestHomePopup#test_108263_home_popup", "labels": [{"name": "story", "value": "首页活动弹窗"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_home.dweb_popup"}, {"name": "suite", "value": "test_108263_home_popup"}, {"name": "subSuite", "value": "TestHomePopup"}, {"name": "host", "value": "SHLAP10453"}, {"name": "thread", "value": "1436-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_home.dweb_popup.test_108263_home_popup"}]}