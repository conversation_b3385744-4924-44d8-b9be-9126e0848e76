{"name": "popup接口监听测试", "status": "passed", "description": "\n        独立测试popup接口监听功能\n        ", "start": 1752812293231, "stop": 1752812302349, "uuid": "8b655ef3-9159-42bc-b00a-2c940fa3764b", "historyId": "4d2a846ae6ec56947b41d6290eb585f7", "testCaseId": "4d2a846ae6ec56947b41d6290eb585f7", "fullName": "src.Dweb.EC.testcases.dweb_home.dweb_popup.test_108263_home_popup.TestHomeActivityPopup#test_popup_api_monitoring", "labels": [{"name": "story", "value": "首页活动弹窗测试"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_home.dweb_popup"}, {"name": "suite", "value": "test_108263_home_popup"}, {"name": "subSuite", "value": "TestHomeActivityPopup"}, {"name": "host", "value": "SHLAP10453"}, {"name": "thread", "value": "15448-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_home.dweb_popup.test_108263_home_popup"}]}