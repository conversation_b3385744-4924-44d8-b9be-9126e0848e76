{"name": "zipcode切换功能独立测试", "status": "passed", "description": "\n        独立测试zipcode切换功能\n        ", "start": 1752806927168, "stop": 1752806970163, "uuid": "8a484ba2-6711-4de0-b5e1-c12ed4a2cf04", "historyId": "e6c186515fc8d8a9089c55fcf5599e52", "testCaseId": "e6c186515fc8d8a9089c55fcf5599e52", "fullName": "src.Dweb.EC.testcases.dweb_home.dweb_popup.test_108263_home_popup.TestHomeActivityPopup#test_zipcode_switch_functionality", "labels": [{"name": "story", "value": "首页活动弹窗测试"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_regression"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_home.dweb_popup"}, {"name": "suite", "value": "test_108263_home_popup"}, {"name": "subSuite", "value": "TestHomeActivityPopup"}, {"name": "host", "value": "SHLAP10453"}, {"name": "thread", "value": "23532-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_home.dweb_popup.test_108263_home_popup"}]}