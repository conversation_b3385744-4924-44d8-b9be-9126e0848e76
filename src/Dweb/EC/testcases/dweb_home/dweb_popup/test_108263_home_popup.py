import allure
import pytest
from src.Dweb.EC.dweb_pages.dweb_page_home.dweb_page_home import DWebHomePage
from src.config.weee.log_help import log


@allure.story("首页活动弹窗测试")
class TestHomeActivityPopup:
    pytestmark = [pytest.mark.dweb_regression]

    @pytest.fixture(scope='class')
    def setup(self, page: dict, pc_autotest_header):
        hp = DWebHomePage(page["page"], pc_autotest_header, page.get("context"))
        yield hp

    @allure.title("首页活动弹窗功能完整流程测试")
    def test_108263_home_activity_popup_flow(self, setup, login_trace):
        """
        测试首页活动弹窗完整流程
        1. 打开首页检查popup接口调用
        2. 如果popup_exist=true，测试弹窗点击跳转
        3. 如果popup_exist=false，切换zipcode测试
        4. 依次测试99348和46311两个zipcode
        5. 连续两个zipcode都没有popup则跳过
        """
        log.info("开始测试首页活动弹窗完整流程")

        # 监听popup接口调用
        popup_responses = []

        def handle_response(response):
            if "/ec/activity/popup/page" in response.url and "page=page_home" in response.url:
                try:
                    response_data = response.json()
                    popup_responses.append({
                        'url': response.url,
                        'data': response_data,
                        'popup_exist': response_data.get('object', {}).get('popup_exist', False)
                    })
                    log.info(
                        f"捕获到popup接口: popup_exist={response_data.get('object', {}).get('popup_exist', False)}")
                except:
                    log.warning("无法解析popup接口响应")

        setup.page.on("response", handle_response)

        # 1. 打开首页
        setup.page.goto("https://www.sayweee.com/en")
        setup.page.wait_for_timeout(8000)
        setup.page.wait_for_load_state("networkidle", timeout=30000)

      

        # 2. 检查初始popup状态
        initial_popup_exist = self.check_popup_api_response(popup_responses)
        log.info(f"初始页面popup状态: {initial_popup_exist}")

        if initial_popup_exist:
            # 3. 如果有popup，测试点击跳转功能
            log.info("检测到popup，开始测试点击跳转功能")
            self.test_popup_click_navigation(setup)
        else:
            # 4. 如果没有popup，开始zipcode切换测试
            log.info("未检测到popup，开始zipcode切换测试")
            self.test_zipcode_switching_for_popup(setup, popup_responses)

        log.info("首页活动弹窗完整流程测试完成")

    def check_popup_api_response(self, popup_responses):
        """检查popup接口响应中的popup_exist状态"""
        if popup_responses:
            latest_response = popup_responses[-1]
            return latest_response.get('popup_exist', False)
        return False

    def test_popup_click_navigation(self, setup):
        """测试popup点击跳转功能"""
        try:
            # 检查popup是否显示
            popup_link = setup.page.get_by_test_id("wid-activity-image-content-image-link")
            assert popup_link.is_visible(timeout=5000), "popup未显示"

            # 记录当前URL
            current_url = setup.page.url
            log.info(f"点击popup前URL: {current_url}")

            # 点击popup
            popup_link.click()
            setup.page.wait_for_timeout(5000)

            # 验证页面跳转
            new_url = setup.page.url
            log.info(f"点击popup后URL: {new_url}")

            assert current_url != new_url, "点击popup后页面未跳转"
            log.info("popup点击跳转功能测试通过")

        except Exception as e:
            log.error(f"popup点击跳转测试失败: {str(e)}")
            raise

    def test_zipcode_switching_for_popup(self, setup, popup_responses):
        """测试zipcode切换寻找popup"""
        test_zipcodes = ["99348", "46311"]
        popup_found_count = 0

        for zipcode in test_zipcodes:
            log.info(f"开始测试zipcode: {zipcode}")

            # 清空之前的响应记录
            popup_responses.clear()

            # 切换zipcode
            switch_success = self.switch_zipcode(setup, zipcode)
            if not switch_success:
                log.warning(f"切换zipcode {zipcode} 失败")
                continue

            # 等待页面加载和接口调用
            setup.page.wait_for_timeout(5000)
            setup.page.wait_for_load_state("networkidle", timeout=15000)

            # 检查popup状态
            popup_exist = self.check_popup_api_response(popup_responses)
            log.info(f"zipcode {zipcode} popup状态: {popup_exist}")

            if popup_exist:
                popup_found_count += 1
                log.info(f"在zipcode {zipcode} 下发现popup，测试点击功能")
                self.test_popup_click_navigation(setup)
                break
            else:
                log.info(f"zipcode {zipcode} 未发现popup")

        # 如果连续两个zipcode都没有popup，跳过测试
        if popup_found_count == 0:
            log.warning("连续两个zipcode都未发现popup")
            pytest.skip("连续两个zipcode都未发现popup，跳过此测试用例")

    def switch_zipcode(self, setup, zipcode):
        """切换zipcode"""
        try:
            log.info(f"开始切换zipcode到: {zipcode}")

            # 1. 点击zipcode按钮
            zipcode_modal_btn = setup.page.get_by_test_id("wid-modal-zip-code")
            assert zipcode_modal_btn.is_visible(timeout=5000), "zipcode按钮不可见"
            zipcode_modal_btn.click()
            setup.page.wait_for_timeout(2000)

            # 2. 输入zipcode
            zipcode_input = setup.page.get_by_test_id("wid-input-zipcode-input")
            assert zipcode_input.is_visible(timeout=5000), "zipcode输入框不可见"
            zipcode_input.clear()
            zipcode_input.fill(zipcode)
            setup.page.wait_for_timeout(1000)

            # 3. 点击确定按钮
            confirm_btn = setup.page.get_by_test_id("btn-zipcode-btn")
            assert confirm_btn.is_visible(timeout=5000), "确定按钮不可见"
            confirm_btn.click()
            setup.page.wait_for_timeout(3000)

            log.info(f"zipcode切换到 {zipcode} 完成")
            return True

        except Exception as e:
            log.error(f"切换zipcode失败: {str(e)}")
            return False

    def close_advertisement_popup(self, page):
        """关闭广告弹窗"""
        try:
            ad_close = page.locator("//div[@class='ant-modal-body']//img[@alt='Close']").first
            if ad_close.is_visible(timeout=3000):
                ad_close.click()
                page.wait_for_timeout(2000)
                log.info("关闭了广告弹窗")
        except:
            log.info("没有广告弹窗需要关闭")

    @allure.title("zipcode切换功能独立测试")
    def test_zipcode_switch_functionality(self, setup, login_trace):
        """
        独立测试zipcode切换功能
        """
        log.info("开始测试zipcode切换功能")

        # 打开首页
        setup.page.goto("https://www.sayweee.com/en")
        setup.page.wait_for_timeout(5000)
        setup.page.wait_for_load_state("networkidle", timeout=30000)

        # 测试切换到99348
        switch_result = self.switch_zipcode(setup, "99348")
        assert switch_result, "切换zipcode到99348失败"

        # 测试切换到46311
        switch_result = self.switch_zipcode(setup, "46311")
        assert switch_result, "切换zipcode到46311失败"

        log.info("zipcode切换功能测试完成")

    @allure.title("popup接口监听测试")
    def test_popup_api_monitoring(self, setup, login_trace):
        """
        独立测试popup接口监听功能
        """
        log.info("开始测试popup接口监听")

        popup_api_calls = []

        def handle_response(response):
            if "/ec/activity/popup/page" in response.url and "page=page_home" in response.url:
                popup_api_calls.append(response.url)
                log.info(f"监听到popup接口调用: {response.url}")

        setup.page.on("response", handle_response)

        # 打开首页
        setup.page.goto("https://www.sayweee.com/en")
        setup.page.wait_for_timeout(8000)
        setup.page.wait_for_load_state("networkidle", timeout=30000)

        # 验证是否监听到接口调用
        assert len(popup_api_calls) > 0, "未监听到popup接口调用"
        log.info(f"成功监听到 {len(popup_api_calls)} 次popup接口调用")