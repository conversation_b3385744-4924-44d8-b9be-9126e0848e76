import allure
import pytest
from src.config.weee.log_help import log
from src.Dweb.EC.dweb_ele.dweb_home.dweb_home_ele import *


@allure.story("首页活动弹窗")
class TestHomePopup:
    pytestmark = [pytest.mark.dweb_regression]

    @allure.title("首页活动弹窗功能测试")
    def test_108263_home_popup(self, page: dict, pc_autotest_header, login_trace):
        """
        测试首页活动弹窗功能
        1. 访问首页获取活动弹窗接口
        2. 如果弹出popup先关闭
        3. 切换zipcode触发popup
        4. 点击popup跳转到活动页
        """
        test_page = page["page"]
        log.info("开始测试首页活动弹窗")

        # 1. 访问首页
        test_page.goto("https://www.sayweee.com/en?referral_id=9664862")
        test_page.wait_for_timeout(8000)
        test_page.wait_for_load_state("networkidle", timeout=60000)

        # 关闭可能存在的广告弹窗
        try:
            ad_close = test_page.locator("//div[@class='ant-modal-body']//img[@alt='Close']").first
            if ad_close.is_visible(timeout=3000):
                ad_close.click()
                test_page.wait_for_timeout(2000)
        except:
            log.info("没有广告弹窗需要关闭")

        # 2. 检查是否有活动弹窗
        popup_exists = self.check_activity_popup(test_page)
        log.info(f"首页弹窗状态: {popup_exists}")

        # 3. 如果有弹窗，先关闭它
        if popup_exists:
            close_result = self.close_activity_popup(test_page)
            log.info(f"关闭弹窗结果: {close_result}")
            assert close_result, "关闭弹窗失败"

        # 4. 切换zipcode重新触发弹窗
        zipcodes = ["98011", "95008", "46311", "99348"]
        popup_triggered = False

        for zipcode in zipcodes:
            log.info(f"尝试切换zipcode: {zipcode}")
            self.switch_zipcode(test_page, zipcode)

            # 检查是否触发了弹窗
            popup_exists = self.check_activity_popup(test_page)
            if popup_exists:
                log.info(f"zipcode {zipcode} 成功触发弹窗")
                popup_triggered = True
                break

        # 5. 如果成功触发弹窗，点击跳转到活动页
        if popup_triggered:
            current_url = test_page.url
            click_result = self.click_activity_popup(test_page)
            assert click_result, "点击弹窗失败"

            # 等待页面跳转
            test_page.wait_for_timeout(5000)
            new_url = test_page.url

            # 验证页面已跳转
            assert current_url != new_url, "点击弹窗后页面未跳转"
            log.info(f"成功跳转到活动页: {new_url}")
        else:
            log.warning("所有zipcode都未能触发弹窗")
            pytest.skip("未能触发活动弹窗，跳过测试")

        log.info("首页活动弹窗测试完成")

    def check_activity_popup(self, page):
        """检查活动弹窗是否存在"""
        try:
            popup = page.locator(ele_home_activity_popup_image).first
            return popup.is_visible(timeout=3000)
        except:
            return False

    def close_activity_popup(self, page):
        """关闭活动弹窗"""
        try:
            close_button = page.locator(ele_home_activity_popup_close_button).first
            if close_button.is_visible(timeout=3000):
                close_button.click()
                page.wait_for_timeout(2000)
                return True
            return False
        except:
            return False

    def click_activity_popup(self, page):
        """点击活动弹窗跳转到活动页"""
        try:
            popup_image = page.locator(ele_home_activity_popup_image).first
            if popup_image.is_visible(timeout=3000):
                popup_image.click()
                page.wait_for_timeout(3000)
                return True
            return False
        except:
            return False

    def switch_zipcode(self, page, zipcode):
        """切换zipcode"""
        try:
            # 点击zipcode区域
            page.locator(ele_home_zipcode).click()
            page.wait_for_timeout(2000)

            # 输入新的zipcode
            page.locator(ele_home_enter_your_zipcode).fill(zipcode)

            # 点击确认按钮
            page.locator(ele_home_confirm_zipcode).click()
            page.wait_for_timeout(3000)
        except Exception as e:
            log.info(f"切换zipcode失败: {str(e)}")