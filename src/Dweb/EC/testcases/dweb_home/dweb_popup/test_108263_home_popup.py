import pytest
from src.Dweb.EC.dweb_pages.dweb_page_home.dweb_page_home import DWebHomePage
from src.config.weee.log_help import log


class TestHomePopup:

    def test_108263_home_popup(self, dweb_page_home: DWebHomePage):
        """
        测试首页活动弹窗功能
        1. 访问首页获取活动弹窗接口
        2. 如果弹出popup先关闭
        3. 切换zipcode触发popup
        4. 点击popup跳转到活动页
        """
        log.info("开始测试首页活动弹窗")

        # 1. 访问首页并检查弹窗
        popup_exists = dweb_page_home.handle_home_activity_popup()
        log.info(f"首页弹窗状态: {popup_exists}")

        # 2. 如果有弹窗，先关闭它
        if popup_exists:
            close_result = dweb_page_home.close_activity_popup()
            log.info(f"关闭弹窗结果: {close_result}")
            assert close_result, "关闭弹窗失败"

        # 3. 切换zipcode重新触发弹窗
        zipcodes = ["98011", "95008", "46311", "99348"]
        popup_triggered = False

        for zipcode in zipcodes:
            log.info(f"尝试切换zipcode: {zipcode}")
            dweb_page_home.switch_zipcode(zipcode)

            # 检查是否触发了弹窗
            popup_exists = dweb_page_home.check_activity_popup()
            if popup_exists:
                log.info(f"zipcode {zipcode} 成功触发弹窗")
                popup_triggered = True
                break

        # 4. 如果成功触发弹窗，点击跳转到活动页
        if popup_triggered:
            current_url = dweb_page_home.page.url
            click_result = dweb_page_home.click_activity_popup()
            assert click_result, "点击弹窗失败"

            # 等待页面跳转
            dweb_page_home.page.wait_for_timeout(3000)
            new_url = dweb_page_home.page.url

            # 验证页面已跳转
            assert current_url != new_url, "点击弹窗后页面未跳转"
            log.info(f"成功跳转到活动页: {new_url}")
        else:
            log.warning("所有zipcode都未能触发弹窗")
            pytest.skip("未能触发活动弹窗，跳过测试")

        log.info("首页活动弹窗测试完成")