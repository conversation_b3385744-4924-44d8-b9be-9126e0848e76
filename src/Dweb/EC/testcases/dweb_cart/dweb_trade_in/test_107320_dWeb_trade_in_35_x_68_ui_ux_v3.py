import allure
import pytest

from src.Dweb.EC.dweb_ele.dweb_cart import dweb_cart_ele
from src.Dweb.EC.dweb_pages.dweb_page_cart.dweb_page_cart import DWebCartPage
from src.Dweb.EC.dweb_pages.dweb_page_cart.dweb_page_trade_in import DWebTradeInPage
from playwright.sync_api import Page
from src.config.weee.log_help import log
from src.common.commfunc import empty_cart


@allure.story("PC购物车-金额小于$35时显示免运费banner验证")
class TestDWebEmptyCartUIUX:
    pytestmark = [pytest.mark.pccart, pytest.mark.regression, pytest.mark.transaction, pytest.mark.suqin]
    @allure.title("PC购物车-金额小于$35时显示免运费banner验证")
    def test_107320_dWeb_trade_in_low_35_ui_ux_v3(self, page: dict, pc_autotest_header, login_trace):
        """
        [107320][dWeb]-金额小于$35时显示免运费banner验证
        测试步骤：
        1、访问https://www.sayweee.com/en/cart购物车页面，
        2、清除购物车
        3、滚动到购物车底部推荐模块 ele_cart_recommendations
        4、加购推荐商品不超过35 ele_recommend_module_card 不能超过35
        5、判断购物车商品金额ele_cart_normal_item_total小于$35
        6、会显示免运费banner 入口
        """
        p: Page = page.get("page")
        c = page.get("context")
        cart_page = DWebCartPage(p, pc_autotest_header, browser_context=c, page_url="/cart")
        trade_in_page = DWebTradeInPage(p, pc_autotest_header)

        # 1-2. 访问购物车页面并清除购物车
        try:
            empty_cart(pc_autotest_header)
            p.reload()
            p.wait_for_timeout(2000)
            log.info("清空购物车成功")
        except Exception as e:
            log.info(f"清空购物车发生异常: {str(e)}")
        
        # 断言空购物车状态
        assert cart_page.is_empty_cart(), "购物车不为空"
        log.info("验证空购物车状态成功")
        
        # 3. 滚动到购物车底部推荐模块
        cart_page.scroll_to_recommendations()
        log.info("滚动到购物车底部推荐模块")
        p.wait_for_timeout(2000)
        # 加购推荐商品
        
        # 4. 加购推荐商品，确保金额不超过$35
        assert cart_page.add_recommendation_product(), "加购推荐商品失败"
        log.info("成功加购推荐商品")
        
        # 5. 判断购物车商品金额
        cart_amount = cart_page.get_normal_cart_amount()
        log.info(f"当前购物车金额: ${cart_amount}")
        
        # 如果金额超过$35，移除商品直到金额小于$35
        while cart_amount >= 35:
            cart_page.remove_normal_cart_item(0)
            cart_amount = cart_page.get_normal_cart_amount()
            log.info(f"移除商品后，当前购物车金额: ${cart_amount}")
        
        # 确认金额小于$35
        assert cart_amount < 35, f"购物车金额应小于$35，实际为${cart_amount}"
        log.info(f"确认购物车金额小于$35: ${cart_amount}")
        
        # 6. 验证显示免运费banner
        assert cart_page.is_free_shipping_banner_visible(), "购物车金额小于$35时未显示免运费banner"
        banner_text = cart_page.get_free_shipping_banner_text()
        # assert "$35" in banner_text, "免运费banner文案不包含$35"
        log.info(f"验证购物车金额小于$35时显示免运费banner: {banner_text}")

    pytestmark = [pytest.mark.pccart, pytest.mark.toddo, pytest.mark.transaction, pytest.mark.suqin]
    @allure.title("PC购物车-金额小于$68时换购验证")
    def test_107320_dWeb_trade_in_low_68_ui_ux_v3(self, page: dict, pc_autotest_header, login_trace):
        """
        [107320][dWeb]-金额小于$68时换购验证
        测试步骤：
        1、访问https://www.sayweee.com/en/cart购物车页面，
        2、清除购物车
        3、滚动到购物车底部推荐模块 ele_cart_recommendations
        4、加购推荐商品大于35 小于68 ele_recommend_module_card
        5、判断购物车商品金额ele_cart_normal_item_total大于35小于68
        6、会显示显示换购入口banner ele_cart_trade_in_normal
        7、此时banner文案为$x away from unlocking more discounts!
        8、点击换购入口ele_cart_trade_in_normal，右侧弹出换购列表ele_trade_in
        9、此时换购商品ele_trade_in_products不可加购
        10、关闭右侧的换购ele_trade_in_close
        """
        p: Page = page.get("page")
        c = page.get("context")
        cart_page = DWebCartPage(p, pc_autotest_header, browser_context=c, page_url="/cart")
        trade_in_page = DWebTradeInPage(p, pc_autotest_header)

        # 1-2. 访问购物车页面并清除购物车
        try:
            empty_cart(pc_autotest_header)
            p.reload()
            p.wait_for_timeout(2000)
            log.info("清空购物车成功")
        except Exception as e:
            log.info(f"清空购物车发生异常: {str(e)}")

        # 断言空购物车状态
        assert cart_page.is_empty_cart(), "购物车不为空"
        log.info("验证空购物车状态成功")

        # 3. 滚动到购物车底部推荐模块
        cart_page.scroll_to_recommendations()
        log.info("滚动到购物车底部推荐模块")
        p.wait_for_timeout(2000)
        # 加购推荐商品
        # 3. 滚动到购物车底部推荐模块
        cart_page.scroll_to_recommendations()
        log.info("滚动到购物车底部推荐模块")
        p.wait_for_timeout(2000)

        # 4. 加购推荐商品，确保金额在$35-$68之间
        # 先加购一些商品
        assert cart_page.add_recommendation_product(), "加购推荐商品失败"
        log.info("成功加购推荐商品")

        # 5. 判断购物车商品金额
        cart_amount = cart_page.get_normal_cart_amount()
        log.info(f"当前购物车金额: ${cart_amount}")

        # 如果金额小于$35，继续加购直到大于$35
        while cart_amount < 35:
            cart_page.scroll_to_recommendations()
            p.wait_for_timeout(1000)
            assert cart_page.add_recommendation_product(), "加购推荐商品失败"
            # 刷新页面
            p.reload()
            p.wait_for_timeout(2000)
            cart_amount = cart_page.get_normal_cart_amount()
            log.info(f"加购后，当前购物车金额: ${cart_amount}")

        # 如果金额大于等于$68，移除商品直到金额小于$68
        while cart_amount >= 68:
            cart_page.remove_normal_cart_item(0)
            # 刷新页面
            p.reload()
            p.wait_for_timeout(2000)
            cart_amount = cart_page.get_normal_cart_amount()
            log.info(f"移除商品后，当前购物车金额: ${cart_amount}")

        # 确认金额在$35-$68之间
        assert 35 <= cart_amount < 68, f"购物车金额应在$35-$68之间，实际为${cart_amount}"
        log.info(f"确认购物车金额在$35-$68之间: ${cart_amount}")

        # 6-7. 验证显示换购入口banner及文案
        assert trade_in_page.is_normal_cart_trade_in_banner_visible(), "购物车金额在$35-$68之间时未显示换购入口banner"
        banner_text = trade_in_page.get_normal_cart_trade_in_banner_text()
        assert "away from unlocking more discounts" in banner_text, "换购入口banner文案不正确"
        log.info(f"验证购物车金额在$35-$68之间时显示换购入口banner: {banner_text}")

        # 8-9. 点击换购入口，验证右侧弹出换购列表，并且换购商品不可加购
        trade_in_status = trade_in_page.open_trade_in_drawer_and_check()
        assert trade_in_status["visible"], "点击换购入口后未弹出换购列表"
        assert len(trade_in_status["products"]) > 0, "换购列表为空"
        assert not trade_in_status["can_add_to_cart"], "购物车金额在$35-$68之间时，换购商品应该不可加购"
        log.info("验证购物车金额在$35-$68之间时，换购商品不可加购")

        # 10. 关闭右侧的换购
        assert trade_in_page.close_trade_in_drawer(), "关闭换购列表失败"
        log.info("成功关闭换购列表")

        log.info("PC购物车-金额小于$68时换购验证完成")

    pytestmark = [pytest.mark.pccart, pytest.mark.toddo, pytest.mark.transaction, pytest.mark.suqin]
    @allure.title("PC购物车-金额大于$68时换购验证")
    def test_107320_dWeb_trade_in_more_68_ui_ux_v3(self, page: dict, pc_autotest_header, login_trace):
        """
        [107320][dWeb]-金额大于$68时换购验证
        测试步骤：
        1、访问https://www.sayweee.com/en/cart购物车页面，
        2、清除购物车
        3、滚动到购物车底部推荐模块 ele_cart_recommendations
        4、加购推荐商品大于68 ele_recommend_module_card
        5、显示换购入口banner 文案为：You have selected 0/5 special deals!
        6、点击换购入口，右侧再次弹出换购列表ele_trade_in，
        7、此时换购商品ele_trade_in_products可加购
        8、加购换购商品ele_trade_in_products，商品会进入购物车
        9、注意，换购商品最多可支持5件，加购5件之后不可再加购，
        10、回到购物车，文案会更新为You have selected 5/5 special deals!
        """
        p: Page = page.get("page")
        c = page.get("context")
        cart_page = DWebCartPage(p, pc_autotest_header, browser_context=c, page_url="/cart")
        trade_in_page = DWebTradeInPage(p, pc_autotest_header)

        # 1-2. 访问购物车页面并清除购物车
        try:
            empty_cart(pc_autotest_header)
            p.reload()
            p.wait_for_timeout(2000)
            log.info("清空购物车成功")
        except Exception as e:
            log.info(f"清空购物车发生异常: {str(e)}")

        # 断言空购物车状态
        assert cart_page.is_empty_cart(), "购物车不为空"
        log.info("验证空购物车状态成功")

        # 3. 滚动到购物车底部推荐模块
        cart_page.scroll_to_recommendations()
        log.info("滚动到购物车底部推荐模块")
        p.wait_for_timeout(2000)

        # 4. 加购推荐商品，确保金额大于$68
        # 先加购一些商品
        assert cart_page.add_recommendation_product(), "加购推荐商品失败"
        # 刷新页面
        p.reload()
        p.wait_for_timeout(2000)
        log.info("成功加购推荐商品")

        # 判断购物车商品金额
        cart_amount = cart_page.get_normal_cart_amount()
        log.info(f"当前购物车金额: ${cart_amount}")

        # 如果金额小于$68，继续加购直到大于$68
        while cart_amount < 68:
            cart_page.scroll_to_recommendations()
            p.wait_for_timeout(1000)
            assert cart_page.add_recommendation_product(), "加购推荐商品失败"
            # 刷新页面
            p.reload()
            p.wait_for_timeout(2000)
            cart_amount = cart_page.get_normal_cart_amount()
            log.info(f"加购后，当前购物车金额: ${cart_amount}")

        # 确认金额大于$68
        assert cart_amount >= 68, f"购物车金额应大于等于$68，实际为${cart_amount}"
        log.info(f"确认购物车金额大于等于$68: ${cart_amount}")

        # 5. 验证显示换购入口banner及文案
        assert trade_in_page.is_normal_cart_trade_in_banner_visible(), "购物车金额大于$68时未显示换购入口banner"
        banner_text = trade_in_page.get_normal_cart_trade_in_banner_text()
        assert "You have selected 0/5 special deals" in banner_text, "换购入口banner文案不正确"
        log.info(f"验证购物车金额大于$68时显示换购入口banner: {banner_text}")

        # 6-7. 点击换购入口，验证右侧弹出换购列表，并且换购商品可加购
        trade_in_status = trade_in_page.open_trade_in_drawer_and_check()
        assert trade_in_status["visible"], "点击换购入口后未弹出换购列表"
        assert len(trade_in_status["products"]) > 0, "换购列表为空"
        assert trade_in_status["can_add_to_cart"], "购物车金额大于$68时，换购商品应该可加购"
        log.info("验证购物车金额大于$68时，换购商品可加购")

        # 8-9. 加购换购商品，最多5件
        added_count = trade_in_page.add_trade_in_products(5)
        assert added_count > 0, "未成功加购任何换购商品"
        log.info(f"成功加购{added_count}个换购商品")

        # 验证加购5件后不可再加购（如果有超过5个换购商品）
        if len(trade_in_status["products"]) > 5:
            # 检查第6个换购商品是否不可加购
            sixth_product = trade_in_status["products"][5]
            add_btn = sixth_product.locator("[data-testid='btn-add-to-cart']")
            assert add_btn.is_disabled(), "加购5件换购商品后，第6个换购商品应该不可加购"
            log.info("验证加购5件后不可再加购成功")

        # 10. 关闭换购列表，回到购物车，验证文案更新
        assert trade_in_page.close_trade_in_drawer(), "关闭换购列表失败"
        log.info("成功关闭换购列表")

        # 验证购物车中有换购商品
        trade_in_count = trade_in_page.get_trade_in_products_count()
        assert trade_in_count == added_count, f"购物车中应有{added_count}个换购商品，实际有{trade_in_count}个"
        log.info(f"验证购物车中有{trade_in_count}个换购商品")

        # 验证banner文案更新
        banner_text = trade_in_page.get_normal_cart_trade_in_banner_text()
        assert f"You have selected {added_count}/5 special deals" in banner_text, "换购入口banner文案不正确"
        log.info(f"验证加购换购商品后换购入口banner文案: {banner_text}")

        log.info("PC购物车-金额大于$68时换购验证完成")