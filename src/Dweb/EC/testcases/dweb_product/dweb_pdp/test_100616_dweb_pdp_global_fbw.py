import allure
import pytest

from src.Dweb.EC.dweb_ele.dweb_product import dweb_pdp_ele
from playwright.sync_api import Page

from src.Dweb.EC.dweb_pages.dweb_page_pdp.dweb_page_pdp import DWebPDPPage
from src.common.commonui import scroll_one_page_until


@allure.story("【100616】 验证Global+ FBW 商品PDP查看Fulfilled by weee模块文案 ")
class TestDWebPDPReviewListUIUX:
    pytestmark = [pytest.mark.pcpdp,pytest.mark.dweb_todo, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("【100616】 验证Global+ FBW 商品PDP查看Fulfilled by weee模块文案")
    def test_100616_dWeb_pdp_global_fbw_fulfilled_ui(self, page: dict, pc_autotest_header, login_trace):
        """
        【100616】验证Global+ FBW 商品PDP查看Fulfilled by weee模块文案
        """
        p: Page = page.get("page")
        c = page.get("context")
        # 1.直接进入指定pdp页面
        pdp_page = DWebPDPPage(p, pc_autotest_header, browser_context=c,
                               page_url="/product/Matcha-Houjicha-Tea-Chocolate-16pcs/2846390")
        p.wait_for_timeout(3000)
        # 2.验证Global FBW配送模块存在
        assert pdp_page.FE.ele(dweb_pdp_ele.ele_mod_delivery).is_visible(), "配送模块不可见"

        # 3.验证配送图标存在
        assert pdp_page.FE.ele(dweb_pdp_ele.ele_delivery_icon).is_visible(), "配送图标不可见"

        # 4.验证配送方式文案存在
        delivery_method = pdp_page.FE.ele(dweb_pdp_ele.ele_delivery_method_text)
        assert delivery_method.is_visible(), "配送方式文案不可见"

        # 5.验证配送方式图标存在
        assert pdp_page.FE.ele(dweb_pdp_ele.ele_delivery_method_icon).is_visible(), "配送方式图标不可见"

        # 6.验证销售商信息存在
        seller_info = pdp_page.FE.ele(dweb_pdp_ele.ele_delivery_seller_info)
        assert seller_info.is_visible(), "销售商信息不可见"
        assert seller_info.text_content(), "销售商信息文本为空"

        # 7.验证Fulfilled by weee标签存在
        assert p.locator("//span[contains(text(), 'Fulfilled by weee')]").is_visible() or \
               p.locator("//img[contains(@alt, 'fulfilled by weee')]").is_visible(), "Fulfilled by weee标签不可见"
