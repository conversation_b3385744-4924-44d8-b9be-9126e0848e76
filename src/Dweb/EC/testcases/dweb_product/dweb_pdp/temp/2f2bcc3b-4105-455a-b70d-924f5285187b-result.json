{"name": "【110082】 PDP-Review-晒单数量流程验证", "status": "broken", "statusDetails": {"message": "AttributeError: 'NoneType' object has no attribute 'text_content'", "trace": "self = <src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_110082_dWeb_pdp_review_num_check.TestDWebPDPReviewNumCheck object at 0x0000018FCE854C50>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppData\\Lo...6099.28>>, 'page': <Page url='https://www.sayweee.com/en/product/DuoDuo-Vegetarian-Rice-Mushroom-Buns-Frozen-1/34460'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...XDq3D2GaeyUBD8iSnScaZKqEjw3cI4L4Lh7NgXsYgq1W3ETEQviGVFsiVK1lsNBPKTWKNYMDMRMCxl7VF8LkaHOLRt0l7o5iR-LuKHQ_ri-CBWqs', ...}\nlogin_trace = None\n\n    @allure.title(\"【110082】 PDP-Review-晒单数量流程验证\")\n    def test_110082_dWeb_pdp_review_num_check(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【112145】 PC-PDP-video list UI/UX验证\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n        # 1.直接进入指定pdp页面\n        pdp_page = DWebPDPPage(p, pc_autotest_header, browser_context=c,\n                               page_url=\"/product/DuoDuo-Vegetarian-Rice-Mushroom-Buns-Frozen-1/34460\")\n        # 2.滚动到指定位置\n        scroll_one_page_until(p, dweb_pdp_ele.ele_mod_review)\n        # 3.获取review 上的review 数量\n>       review_num = pdp_page.FE.ele(dweb_pdp_ele.ele_mod_review + u\"//strong\").text_content()\nE       AttributeError: 'NoneType' object has no attribute 'text_content'\n\ntest_110082_dWeb_pdp_review_num_check.py:29: AttributeError"}, "description": "\n        【112145】 PC-PDP-video list UI/UX验证\n        ", "start": 1751620149780, "stop": 1751620208433, "uuid": "7afac76a-834e-419b-90dc-fa0debde1dd3", "historyId": "295a53fab4e949adfa592b568d45976d", "testCaseId": "295a53fab4e949adfa592b568d45976d", "fullName": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_110082_dWeb_pdp_review_num_check.TestDWebPDPReviewNumCheck#test_110082_dWeb_pdp_review_num_check", "labels": [{"name": "story", "value": "【110082】 PDP-Review-晒单数量流程验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pcpdp"}, {"name": "tag", "value": "dweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp"}, {"name": "suite", "value": "test_110082_dWeb_pdp_review_num_check"}, {"name": "subSuite", "value": "TestDWebPDPReviewNumCheck"}, {"name": "host", "value": "SHLAP10453"}, {"name": "thread", "value": "12160-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_110082_dWeb_pdp_review_num_check"}]}