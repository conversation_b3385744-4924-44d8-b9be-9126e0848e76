{"name": "【100616】 验证Global+ FBW 商品PDP查看Fulfilled by weee模块文案", "status": "broken", "statusDetails": {"message": "AttributeError: 'NoneType' object has no attribute 'is_visible'", "trace": "self = <src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_100616_dweb_pdp_global_fbw.TestDWebPDPReviewListUIUX object at 0x0000028DA0A186D0>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppData\\Lo...rsion=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/product/Spicy-Crayfish-Seasoning-1-Pack/2846390'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...oxY-ikou8PcqRYMRzVU3KVutqQirGcVusFxZrwMVZm1oRApAKjypUyiLiJ3YodyQeiMaPlgM5wFaDJUyznFww1Er6QZ80gjoUjHNk9-9xuTCQENE', ...}\nlogin_trace = None\n\n    @allure.title(\"【100616】 验证Global+ FBW 商品PDP查看Fulfilled by weee模块文案\")\n    def test_100616_dWeb_pdp_global_fbw_fulfilled_ui(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【100616】验证Global+ FBW 商品PDP查看Fulfilled by weee模块文案\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n        # 1.直接进入指定pdp页面\n        pdp_page = DWebPDPPage(p, pc_autotest_header, browser_context=c,\n                               page_url=\"/product/Matcha-Houjicha-Tea-Chocolate-16pcs/2846390\")\n        p.wait_for_timeout(3000)\n        # 2.验证Global FBW配送模块存在\n>       assert pdp_page.FE.ele(dweb_pdp_ele.ele_mod_delivery).is_visible(), \"配送模块不可见\"\nE       AttributeError: 'NoneType' object has no attribute 'is_visible'\n\ntest_100616_dweb_pdp_global_fbw.py:27: AttributeError"}, "description": "\n        【100616】验证Global+ FBW 商品PDP查看Fulfilled by weee模块文案\n        ", "start": 1751619177524, "stop": 1751619192763, "uuid": "40f3b66f-3fee-4281-bbc0-055929dc7bc7", "historyId": "5f86e765cffb175c25c84f8c417c18a3", "testCaseId": "5f86e765cffb175c25c84f8c417c18a3", "fullName": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_100616_dweb_pdp_global_fbw.TestDWebPDPReviewListUIUX#test_100616_dWeb_pdp_global_fbw_fulfilled_ui", "labels": [{"name": "story", "value": "【100616】 验证Global+ FBW 商品PDP查看Fulfilled by weee模块文案 "}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pcpdp"}, {"name": "tag", "value": "dweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp"}, {"name": "suite", "value": "test_100616_dweb_pdp_global_fbw"}, {"name": "subSuite", "value": "TestDWebPDPReviewListUIUX"}, {"name": "host", "value": "SHLAP10453"}, {"name": "thread", "value": "24376-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_100616_dweb_pdp_global_fbw"}]}