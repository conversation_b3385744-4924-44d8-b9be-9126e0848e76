{"name": "[101480][dweb]-Global+商品-验证商品PDP-店铺推荐模块UI/UX验证", "status": "broken", "statusDetails": {"message": "AttributeError: 'NoneType' object has no attribute 'is_visible'", "trace": "self = <src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_101480_dweb_pdp_same_vendor_ui_ux.TestDWebPDPSameVendorUIUX object at 0x00000247DC5DD5D0>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppData\\Lo...ion=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/product/SK-II-Skinpower-Airy-Milky-Lotion/2038708'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...fEdqkxd3WvAG_x39LSS5E3zNf1SCNTCJtqbzRgJwIBLLbo1-YuuE3cbIEBq7-rTbESzlLk52_ZReDga_RkaEvcQejFatXYAHOG0ZKUwWfuwln3jI', ...}\nlogin_trace = None\n\n    @allure.title(\"[101480][dweb]-Global+商品-验证商品PDP-店铺推荐模块UI/UX验证\")\n    def test_101480_dweb_pdp_same_vendor_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        \"[101480][dweb]-Global+商品-验证商品PDP-店铺推荐模块UI/UX验证\"\n        测试步骤：\n        1. 访问指定商品PDP页面\n        2. 校验页面基本元素\n        3. 滚动到店铺推荐模块\n        4. 校验店铺推荐模块元素\n        5. 验证商品信息（名称、价格）\n        6. 测试收藏按钮功能\n        7. 测试加购按钮功能\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n    \n        # 1. 直接进入指定pdp页面\n        pdp_page = DWebPDPPage(p, pc_autotest_header, browser_context=c,\n                               page_url=\"/product/SK-II-Skinpower-Airy-Milky-Lotion/2038708\")\n    \n        p.wait_for_timeout(5000)\n    \n        # 2. 滚动到店铺推荐模块\n        scroll_one_page_until(p, dweb_pdp_ele.ele_pdp_same_vendor_card)\n    \n        # 3. 校验店铺推荐模块存在\n        assert pdp_page.FE.ele(dweb_pdp_ele.ele_pdp_same_vendor_card).is_visible(), \"店铺推荐模块不可见\"\n    \n        # 4. 校验店铺推荐标题\n>       assert pdp_page.FE.ele(dweb_pdp_ele.ele_pdp_same_vendor_title).is_visible(), \"店铺推荐标题不可见\"\nE       AttributeError: 'NoneType' object has no attribute 'is_visible'\n\ntest_101480_dweb_pdp_same_vendor_ui_ux.py:43: AttributeError"}, "description": "\n        \"[101480][dweb]-Global+商品-验证商品PDP-店铺推荐模块UI/UX验证\"\n        测试步骤：\n        1. 访问指定商品PDP页面\n        2. 校验页面基本元素\n        3. 滚动到店铺推荐模块\n        4. 校验店铺推荐模块元素\n        5. 验证商品信息（名称、价格）\n        6. 测试收藏按钮功能\n        7. 测试加购按钮功能\n        ", "start": 1751623407635, "stop": 1751623427106, "uuid": "49935604-090c-4cf0-8d6e-e2d1411ea053", "historyId": "ba2c7a7bf4001cfafbcc89e6f461da13", "testCaseId": "ba2c7a7bf4001cfafbcc89e6f461da13", "fullName": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_101480_dweb_pdp_same_vendor_ui_ux.TestDWebPDPSameVendorUIUX#test_101480_dweb_pdp_same_vendor_ui_ux", "labels": [{"name": "story", "value": "[101480][dweb]-Global+商品-验证商品PDP-店铺推荐模块UI/UX验证"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pcpdp"}, {"name": "tag", "value": "dweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp"}, {"name": "suite", "value": "test_101480_dweb_pdp_same_vendor_ui_ux"}, {"name": "subSuite", "value": "TestDWebPDPSameVendorUIUX"}, {"name": "host", "value": "SHLAP10453"}, {"name": "thread", "value": "27168-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_101480_dweb_pdp_same_vendor_ui_ux"}]}