{"name": "【100616】 验证Global+ FBW 商品PDP查看Fulfilled by weee模块文案", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium-1091\\chrome-win\\chrome.exe\n╔════════════════════════════════════════════════════════════╗\n║ Looks like Playwright was just installed or updated.       ║\n║ Please run the following command to download new browsers: ║\n║                                                            ║\n║     playwright install                                     ║\n║                                                            ║\n║ <3 Playwright Team                                         ║\n╚════════════════════════════════════════════════════════════╝", "trace": "playwright = <playwright._impl._playwright.Playwright object at 0x000001C950700190>\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...snSRpmjWFTYu937GOSdqxD7HiEXg6jJAGrRDZz1sLrRXNQNvGd6qQzLbGwAGP7fVPfTHFEFqTYQblqumPKpOs2aVEZV4emFEVbrHEQEfvik1zyyw', ...}\nporder = {'message': None, 'message_id': '10000', 'object': {'addr_address': None, 'addr_city': 'Fremont', 'addr_country': '2', 'addr_state': '41', ...}, 'result': True, ...}\n\n    @pytest.fixture(scope='session')\n    def page(playwright, pc_autotest_header, porder) -> Page:\n        # browser = playwright.chromium.launch(executable_path=r'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', headless=False, args=['--start-maximized'])\n>       browser = playwright.chromium.launch(headless=bool(os.getenv(\"headless\", False)), args=['--start-maximized', '--incognito'])\n\n..\\..\\..\\conftest.py:152: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:14806: in launch\n    self._sync(\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_browser_type.py:95: in launch\n    Browser, from_channel(await self._channel.send(\"launch\", params))\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x000001C9504416D0>\nmethod = 'launch'\nparams = {'args': ['--start-maximized', '--incognito'], 'headless': False}\nreturn_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.Error: Executable doesn't exist at C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium-1091\\chrome-win\\chrome.exe\nE       ╔════════════════════════════════════════════════════════════╗\nE       ║ Looks like Playwright was just installed or updated.       ║\nE       ║ Please run the following command to download new browsers: ║\nE       ║                                                            ║\nE       ║     playwright install                                     ║\nE       ║                                                            ║\nE       ║ <3 Playwright Team                                         ║\nE       ╚════════════════════════════════════════════════════════════╝\n\n..\\..\\..\\..\\..\\..\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: Error"}, "description": "\n        【100616】验证Global+ FBW 商品PDP查看Fulfilled by weee模块文案\n        ", "start": 1751617398612, "stop": 1751617398612, "uuid": "2d8ed9a2-7aa3-438d-a4df-19efd5477f0d", "historyId": "5f86e765cffb175c25c84f8c417c18a3", "testCaseId": "5f86e765cffb175c25c84f8c417c18a3", "fullName": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_100616_dweb_pdp_global_fbw.TestDWebPDPReviewListUIUX#test_100616_dWeb_pdp_global_fbw_fulfilled_ui", "labels": [{"name": "story", "value": "【100616】 验证Global+ FBW 商品PDP查看Fulfilled by weee模块文案 "}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "pcpdp"}, {"name": "tag", "value": "dweb_regression"}, {"name": "tag", "value": "transaction"}, {"name": "tag", "value": "suqin"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp"}, {"name": "suite", "value": "test_100616_dweb_pdp_global_fbw"}, {"name": "subSuite", "value": "TestDWebPDPReviewListUIUX"}, {"name": "host", "value": "SHLAP10453"}, {"name": "thread", "value": "35180-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_product.dweb_pdp.test_100616_dweb_pdp_global_fbw"}]}