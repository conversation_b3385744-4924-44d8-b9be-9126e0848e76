"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   test_112913_dweb_checkout_single_fresh_cart_ui_ux.py
@Description    :  PC结算单个生鲜购物车UI/UX验证
@CreateTime     :  2025/6/10 14:30
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/6/10 14:30
"""
import allure
import pytest
from playwright.sync_api import Page

from src.Dweb.EC.dweb_pages.dweb_page_cart.dweb_page_cart import DWebCartPage
from src.Dweb.EC.dweb_pages.dweb_page_category.dweb_page_category import DWebCategorypage
from src.Dweb.EC.dweb_pages.dweb_page_checkout.dweb_page_checkout import DWebCheckoutPage
from src.common.commfunc import empty_cart
from src.config.weee.log_help import log


@allure.story("PC结算页-单个生鲜购物车UI/UX验证")
class TestWebCheckoutSingleFreshCartUIUX:
    pytestmark = [pytest.mark.dweb_todo,pytest.mark.zhuli]
    
    @allure.title("PC结算单个生鲜购物车UI/UX验证")
    @pytest.mark.present
    def test_112913_dweb_checkout_single_fresh_cart_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        【112913】 PC结算单个生鲜购物车UI/UX验证
        
        测试步骤:
        1. 清空购物车
        2. 从分类页加购生鲜商品
        3. 进入购物车页面
        4. 点击结算按钮进入结算页
        5. 验证结算页标题
        6. 验证配送信息区域
        7. 验证支付信息区域
        8. 验证订单摘要信息
        9. 验证购物车商品列表
        10. 验证下单按钮
        """
        p: Page = page.get("page")
        c = page.get("context")
        
        # 1. 清空购物车
        with allure.step("清空购物车"):
            try:
                empty_cart(pc_autotest_header)
                log.info("购物车清空成功")
            except Exception as e:
                log.error(f"清空购物车失败: {str(e)}")
                raise
        
        # 2. 从分类页加购生鲜商品
        with (allure.step("从分类页加购生鲜商品")):
            # 构造的分类页面
            category_page = DWebCategorypage(p, pc_autotest_header, browser_context=c)
            # 去分类页加购Local类型的商品进购物车
            category_page.add_to_local_product_cart_from_category()
            # .add_to_local_product_cart_from_category()
            log.info("成功加购生鲜商品")
        
        # 3. 进入购物车页面
        # with allure.step("进入购物车页面"):
        #     # 去购物车结算
        #     cart_page = DWebCartPage(p, pc_autotest_header, browser_context=c,page_url="/cart")
        #     p.wait_for_timeout(3000)
        #     log.info("成功进入购物车页面")
        p.wait_for_timeout(2000)
        # 4. 点击结算按钮进入结算页
        with allure.step("点击结算按钮进入结算页"):
            # 构造的购物车页面
            cart_page = DWebCartPage(p, pc_autotest_header, browser_context=c,page_url="/cart")
            # 点击结算按钮
            checkout_button = p.get_by_test_id("wid-cart-summary-checkout")
            assert checkout_button.is_visible(), "结算按钮不可见"
            checkout_button.click()
            log.info("成功点击结算按钮")
            p.wait_for_timeout(2000)
            # 处理可能的upsell弹窗
            try:
                upsell_button = p.get_by_test_id("wid-upsell-continue-to-checkout")
                if upsell_button.is_visible(timeout=3000):
                    log.info("检测到upsell弹窗，点击继续结算按钮")
                    upsell_button.click()
                    p.wait_for_timeout(3000)
                else:
                    log.info("未检测到upsell弹窗，直接进行下一步")
            except Exception as e:
                log.debug(f"处理upsell弹窗时出现异常或未检测到upsell弹窗: {str(e)}")
        p.wait_for_timeout(2000)
        # 5. 验证结算页标题//找不到标题
        with allure.step("验证结算页标题"):
            # 构造的结算页页面
            checkout_page = DWebCheckoutPage(p, pc_autotest_header, browser_context=c)
            checkout_page.verify_checkout_title()
            # 处理可能的upsell弹窗
            try:
                upsell_button = p.get_by_test_id("wid-upsell-continue-to-checkout")
                if upsell_button.is_visible(timeout=3000):
                    log.info("检测到upsell弹窗，点击继续结算按钮")
                    upsell_button.click()
                    p.wait_for_timeout(3000)
                else:
                    log.info("未检测到upsell弹窗，直接进行下一步")
            except Exception as e:
                log.debug(f"处理upsell弹窗时出现异常或未检测到upsell弹窗: {str(e)}")
        p.wait_for_timeout(2000)
        # 6. 验证配送信息区域
        with allure.step("验证配送信息区域"):
            shipping_info = checkout_page.verify_shipping_section()
            log.info(f"配送信息: {shipping_info}")
        
        # 7. 验证支付信息区域
        # with allure.step("验证支付信息区域"):
        #     payment_method = checkout_page.verify_payment_section()
        #     log.info(f"支付方式: {payment_method}")
        
        # 8. 验证订单摘要信息
        # with allure.step("验证订单摘要信息"):
        #     order_summary = checkout_page.verify_order_summary()
        #     log.info(f"订单摘要: {order_summary}")
        #
        # 9. 验证购物车商品列表
        # with allure.step("验证购物车商品列表"):
        #     cart_items = checkout_page.verify_cart_items()
        #     log.info(f"购物车商品: {cart_items}")
        #
        p.wait_for_timeout(2000)
        # 10. 验证下单按钮
        with allure.step("验证下单按钮"):
            place_order_btn = checkout_page.verify_place_order_button()
            log.info("下单按钮验证通过")
        
        log.info("PC结算单个生鲜购物车UI/UX验证测试完成")
        # 验证凑单banner是否存在
        # with allure.step("验证凑单banner"):
        #     try:
        #         banner = p.get_by_test_id('wid-checkout-free-shipping-banner')
        #         expect(banner).to_be_visible()
        #         # 验证banner文案包含凑单金额信息
        #         banner_text = banner.text_content()
        #         assert "$35" in banner_text, "凑单banner金额不正确"
        #         log.info("凑单banner验证成功")
        #     except Exception as e:
        #         log.error(f"验证凑单banner失败: {str(e)}")
        #         raise
        # 验证会员等级模块
        # with allure.step("验证会员等级显示"):
        #     try:
        #         # 获取会员等级信息
        #         member_level = p.get_by_test_id('wid-checkout-rewards-header')
        #         level_text = member_level.text_content()
        #
        #         # 根据不同等级验证对应的图标和文案
        #         if "Bronze" in level_text.upper():
        #             # p.get_by_test_id('wid-checkout-rewards-header')
        #             expect(checkout_page.FE.ele("//div[@data-testid='btn-atc-plus']").locator('img')).to_be_visible()
        #             expect(p.locator("text=Bronze Rewards member")).to_be_visible()
        #         elif "Silver" in level_text.upper():
        #             expect(p.get_by_test_id('wid-checkout-rewards-header').locator('img')).to_be_visible()
        #             expect(p.locator("text=Silver Rewards member")).to_be_visible()
        #         elif "Gold" in level_text.upper():
        #             expect(p.get_by_test_id('wid-checkout-rewards-header').locator('img')).to_be_visible()
        #             expect(p.locator("text=Gold Rewards member")).to_be_visible()
        #
        #         log.info(f"会员等级验证成功: {level_text}")
        #     except Exception as e:
        #         log.error(f"验证会员等级显示失败: {str(e)}")
        #         raise

        # with allure.step("验证地址列表功能"):
        #     try:
        #         # 点击地址箭头
        #         address_arrow = p.locator("[data-testid='wid-checkout-address-selector']")
        #         address_arrow.click()
        #         p.wait_for_timeout(1000)
        #
        #         # 验证地址列表是否加载
        #         address_list = p.locator("[data-testid='wid-checkout-address-list']")
        #         expect(address_list).to_be_visible()
        #
        #         # 验证是否至少有一个地址选项
        #         address_items = address_list.locator("li")
        #         expect(address_items).to_have_count(greater_than=0)
        #
        #         log.info("地址列表功能验证成功")
        #     except Exception as e:
        #         log.error(f"验证地址列表功能失败: {str(e)}")
        #         raise
        # with allure.step("验证小费模块功能"):
        #     # 验证小费功能
        #     try:
        #         # 获取当前小费标题
        #         assert "Delivery tip" == p.locator(checkout_elements.ele_checkout_tip_title).text_content()
        #         # 小费模块底部的文案存在
        #         p.locator("//div[@data-testid='wid-checkout-delivery-tip']/div[3]").is_visible()
        #
        #         log.info("小费模块文案验证成功")
        #     except Exception as e:
        #         log.error(f"小费模块文案验证失败: {str(e)}")
        #         raise
        #
        # with allure.step("验证最终总计下面的文案存在"):
        #     # 验证结算功能
        #     try:
        #         # 获取最终总计下面的文案
        #         p.locator(dweb_checkout_ele.ele_checkout_final_text).is_visible()
        #         # 获取提交订单的文案
        #         p.locator(dweb_checkout_ele.ele_checkout_final_submit_text).is_visible()
        #         p.locator("//a[@class='font-medium']").click()
        #         p.wait_for_timeout(2000)
        #
        #         log.info("最终总计下面的文案验证成功")
        #     except Exception as e:
        #         log.error(f"最终总计下面的文案验证验证失败: {str(e)}")
        #         raise

        # tip_amount = p.locator("[data-testid='wid-checkout-tip-amount']")
        # initial_tip = tip_amount.text_content()
        #
        # # 点击小费选择下拉框
        # p.locator("[data-testid='wid-checkout-tip-selector']").click()
        # p.wait_for_timeout(1000)
