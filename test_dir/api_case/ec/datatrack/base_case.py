from clickhouse_connect.driver import Client
from weeeTest import log


class BaseCase:

    base_sql = "select * from data_tracking_all "
    base_params_sql = "select params from data_tracking_all "
    def query_track(self, client: Client, sql):
        # res = None
        try:
            res = client.query(sql)
        except Exception as e:
            log.info("clickhouse查询埋点失败" + str(e))
            return False

        if res:
            return res
        else:
            return False

    def query_track_generator(self, client: Client, sql):
        try:
            res = client.query(sql).result_rows
            if isinstance(res, list):
                for i in res:
                    yield i

        except Exception as e:
            log.info("clickhouse查询埋点失败" + str(e))
            return False


