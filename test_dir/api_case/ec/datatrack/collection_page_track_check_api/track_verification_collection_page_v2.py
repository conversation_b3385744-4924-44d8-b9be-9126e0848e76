import json
from test_dir.api_case.ec.datatrack.datatrack_common_function import TrackCommonVerify


class TrackVerificationCollectionPageV2:
    @staticmethod
    def collection_track_t2_click_action(track: [], page_key, platform, click_type) -> bool:
        flag = False
        for item in track:
            if item.get('l0_page_key') and item.get('l0_page_key') == page_key:
                if item.get("l0_platform") and item.get("l0_platform") == platform:
                    if item.get('params'):
                        track_params = json.loads(item.get('params'))
                        if track_params.get('co') and track_params.get('co').get('target_type'):
                            if track_params.get('co').get('target_type') == 'product':
                                if track_params.get('co').get('click_type') == click_type:
                                    if track_params.get('ctx') and track_params.get('ctx').get('page_target'):
                                        flag = True
        return flag

    @staticmethod
    def collection_track_t2_click_action_share(track, page_key, platform):
        flag = False
        for item in track:
            if item.get('l0_page_key') and item.get('l0_page_key') == page_key:
                if item.get("l0_platform") and item.get("l0_platform") == platform:
                    if item.get('params'):
                        track_params = json.loads(item.get('params'))
                        if track_params.get('co') and track_params.get('co').get('target_type'):
                            if track_params.get('co').get('target_type') == 'collection' and track_params.get('co').get('click_type') == 'share':
                                if track_params.get('ctx') and track_params.get('ctx').get('page_target'):
                                    flag = True
        return flag

    @staticmethod
    def collection_track_t2_cart_action(track, page_key, platform):
        flag = False
        for item in track:
            if item.get('l0_page_key') and item.get('l0_page_key') == page_key:
                if item.get("l0_platform") and item.get("l0_platform") == platform:
                    if item.get('params'):
                        track_params = json.loads(item.get('params'))
                        if 'mod_nm' in track_params.keys() and track_params.get('mod_nm') is None:
                            if track_params.get('ctx') and track_params.get('ctx').get('page_target'):
                                if track_params.get('co') and track_params.get('co').get('source'):
                                    if track_params.get('co').get('source') == f"{page_key}-null-{track_params.get('ctx').get('page_target')}":
                                        flag = True
        return flag

    @staticmethod
    def collection_track_t2_prod_imp(track, page_key, platform):
        flag = False
        for item in track:
            if item.get('l0_page_key') and item.get('l0_page_key') == page_key:
                if item.get("l0_platform") and item.get("l0_platform") == platform:
                    if item.get('params'):
                        track_params = json.loads(item.get('params'))
                        if 'mod_nm' in track_params.keys() and track_params.get('mod_nm') is None:
                            if track_params.get('ctx') and track_params.get('ctx').get('page_target'):
                                if track_params.get('co') and TrackCommonVerify.is_null_in_dict(track_params.get('co')):
                                    flag = True
        return flag



