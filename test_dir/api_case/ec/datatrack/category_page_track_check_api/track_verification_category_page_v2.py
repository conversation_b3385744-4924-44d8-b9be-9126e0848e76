import json
from test_dir.api_case.ec.datatrack.datatrack_common_function import TrackCommonVerify


class TrackVerificationCategoryPageV2:
    @staticmethod
    def category_t2_click_product(track: [], mod_nm, page_key, platform, click_type) -> bool:
        flag = False
        for item in track:
            if item.get('l0_page_key') and item.get('l0_page_key') == page_key:
                if item.get("l0_platform") and item.get("l0_platform") == platform:
                    if item.get('params'):
                        track_params = json.loads(item.get('params'))
                        if track_params.get('mod_nm') == mod_nm:
                            if track_params.get('co') and track_params.get('co').get('target_type'):
                                if track_params.get('co').get('target_type') == 'product':
                                    if track_params.get('co').get('click_type') == click_type:
                                        flag = True
        return flag

    @staticmethod
    def category_t2_click_category(track, mod_nm, page_key, platform):
        flag = False
        for item in track:
            if item.get('l0_page_key') and item.get('l0_page_key') == page_key:
                if item.get("l0_platform") and item.get("l0_platform") == platform:
                    if item.get('params'):
                        track_params = json.loads(item.get('params'))
                        if track_params.get("mod_nm") == mod_nm:
                            if track_params.get('co') and track_params.get('co').get('target_type'):
                                if track_params.get('co').get('target_type') == "category":
                                    flag = True
        return flag

    @staticmethod
    def category_t2_click_filter(track, mod_nm, page_key, platform):
        flag = False
        for item in track:
            if item.get('l0_page_key') and item.get('l0_page_key') == page_key:
                if item.get("l0_platform") and item.get("l0_platform") == platform:
                    if item.get('params'):
                        track_params = json.loads(item.get('params'))
                        if track_params.get("mod_nm") == mod_nm:
                            if track_params.get('co') and track_params.get('co').get('target_type'):
                                if track_params.get('co').get('target_type') == "sort_filter":
                                    if track_params.get('ctx'):
                                        if track_params.get('ctx').get('filter_sub_category') and track_params.get('ctx').get("sort"):
                                            flag = True
        return flag

    @staticmethod
    def category_t2_click_banner(track, mod_nm, page_key, platform):
        flag = False
        for item in track:
            if item.get('l0_page_key') and item.get('l0_page_key') == page_key:
                if item.get("l0_platform") and item.get("l0_platform") == platform:
                    if item.get('params'):
                        track_params = json.loads(item.get('params'))
                        if track_params.get("mod_nm") == mod_nm:
                            if track_params.get('sec_nm') == 'carousel':
                                if track_params.get('co') and track_params.get('co').get('target_type') == 'top_banner':
                                    if track_params.get('ctx'):
                                        if track_params.get('ctx').get('filter_sub_category') and track_params.get('ctx').get("sort"):
                                            flag = True
        return flag

    @staticmethod
    def category_t2_click_l2_icon(track, mod_nm, page_key, platform):
        flag = False
        for item in track:
            if item.get('l0_page_key') and item.get('l0_page_key') == page_key:
                if item.get("l0_platform") and item.get("l0_platform") == platform:
                    if item.get('params'):
                        track_params = json.loads(item.get('params'))
                        if track_params.get("mod_nm") == mod_nm:
                            if track_params.get('co') and track_params.get('co').get('target_type') == 'filter_button':
                                flag = True
        return flag

    @staticmethod
    def category_t2_prod_impose(track, mod_nm, page_key, platform):
        flag = False
        for item in track:
            if item.get('l0_page_key') and item.get('l0_page_key') == page_key:
                if item.get("l0_platform") and item.get("l0_platform") == platform:
                    if item.get('params'):
                        track_params = json.loads(item.get('params'))
                        if track_params.get("mod_nm") == mod_nm and track_params.get("co"):
                            if TrackCommonVerify.is_null_in_dict(track_params.get("co")):
                                if track_params.get('ctx'):
                                    if track_params.get("ctx").get('filter_sub_category') and track_params.get('ctx').get('sort'):
                                        flag = True
        return flag

    @staticmethod
    def category_t2_cart_action(track, mod_nm, page_key, platform):
        flag = False
        for item in track:
            if item.get('l0_page_key') and item.get('l0_page_key') == page_key:
                if item.get("l0_platform") and item.get("l0_platform") == platform:
                    if item.get('params'):
                        track_params = json.loads(item.get('params'))
                        if track_params.get("mod_nm") == mod_nm and track_params.get("co"):
                            if TrackCommonVerify.is_null_in_dict(track_params.get("co")):
                                if track_params.get('ctx'):
                                    if track_params.get("ctx").get('filter_sub_category') and track_params.get('ctx').get('sort'):
                                        flag = True
        return flag

    @staticmethod
    def category_t2_ellipse_imp(track, mod_nm, page_key, platform):
        flag = False
        for item in track:
            if item.get('l0_page_key') and item.get('l0_page_key') == page_key:
                if item.get("l0_platform") and item.get("l0_platform") == platform:
                    if item.get('params'):
                        track_params = json.loads(item.get('params'))
                        if track_params.get("mod_nm") == mod_nm and track_params.get("co"):
                            if track_params.get('co').get('ellipse_type') == 'category':
                                flag = True
        return flag

    @staticmethod
    def category_t2_filter_button_imp(track, mod_nm, page_key, platform):
        flag = False
        for item in track:
            if item.get('l0_page_key') and item.get('l0_page_key') == page_key:
                if item.get("l0_platform") and item.get("l0_platform") == platform:
                    if item.get('params'):
                        track_params = json.loads(item.get('params'))
                        if track_params.get("mod_nm") == mod_nm:
                            if track_params.get('ctx'):
                                if track_params.get("ctx").get('filter_sub_category') and track_params.get('ctx').get('sort'):
                                    flag = True
        return flag

    @staticmethod
    def category_t2_banner_imp(track, mod_nm, page_key, platform):
        flag = False
        for item in track:
            if item.get('l0_page_key') and item.get('l0_page_key') == page_key:
                if item.get("l0_platform") and item.get("l0_platform") == platform:
                    if item.get('params'):
                        track_params = json.loads(item.get('params'))
                        if track_params.get("mod_nm") == mod_nm and track_params.get("sec_nm") == 'carousel':
                            if track_params.get('co') and track_params.get('co').get('banner_type') == 'top_banner':
                                if track_params.get('ctx'):
                                    if track_params.get("ctx").get('filter_sub_category') and track_params.get('ctx').get('sort'):
                                        flag = True
        return flag




