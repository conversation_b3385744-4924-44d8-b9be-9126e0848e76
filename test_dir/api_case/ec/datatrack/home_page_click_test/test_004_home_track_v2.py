import json

import allure
import pytest
import weeeTest
from weeeTest import log

from test_dir.api_case.ec.datatrack.base_case import BaseCase
from test_dir.api_case.ec.datatrack.datatrack_common_function import TrackCommonVerify


@allure.story("home页面埋点查询与校验V2")
class TestHomePageTrackV2(weeeTest.TestCase, BaseCase):
    pytestmark = [pytest.mark.firstpagelevel, pytest.mark.clickhouse]
    platform_and_page_key = [("MWeb", "mweb_home"), ("Android", "android_home"), ("iOS", "ios_home")]

    components = [
        "cm_item_new",
        "cm_item_trending",
        "cm_item_sale",
        "cm_lightning_deals",
        "cm_product_line_tabs_fresh_daily"
    ]



    @allure.step("首页product点击埋点校验V2: platform={platform},page_key={page_key},mod_nm={mod_nm}")
    @pytest.mark.parametrize("platform, page_key", platform_and_page_key)
    @pytest.mark.parametrize("mod_nm", components)
    def test_0010000_home_track_t2_click_product(self, platform, page_key, mod_nm, clickhouse_client, time_range):
        sql = f"""
            select JSONExtractRaw(params, 'mod_nm') as a,  JSONExtractRaw(JSONExtractRaw(params, 'co'), 'target_type') as b from weee_data.data_tracking_all where __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_page_key='{page_key}' and a='"{mod_nm}"' and b='"product"' limit 100
        """
        log.info("sql===> " + sql)
        result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
        log.info("length===> " + str(len(result_rows)))
        assert result_rows, f"没有找到-首页product点击埋点校验V2: platform={platform},page_key={page_key},mod_nm={mod_nm}的埋点"


    @allure.step("首页banner点击埋点校验V2: platform={platform},page_key={page_key},mod_nm=cm_main_banner")
    @pytest.mark.parametrize("platform, page_key", platform_and_page_key)
    def test_0020000_home_track_t2_click_banner(self, platform, page_key, clickhouse_client, time_range):
        sql = f"""
                    select JSONExtractRaw(params, 'mod_nm') as a,  JSONExtractRaw(JSONExtractRaw(params, 'co'), 'target_type') as b, JSONExtractRaw(params, 'co') as c from weee_data.data_tracking_all where __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_page_key='{page_key}' and a='"cm_main_banner"' and b='"home_top_banner"' limit 100
                """
        log.info("sql===> " + sql)
        result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
        log.info("length===> " + str(len(result_rows)))
        assert result_rows, f"没有找到-首页banner点击埋点校验V2: platform={platform},page_key={page_key},mod_nm=cm_main_banner的埋点"
        new_json_list = [json.loads(item[2]) for item in result_rows if item[2].startswith("{")]
        flag = False
        for j in new_json_list:
            if TrackCommonVerify.is_null_in_dict(j):
                flag = True
                break
        assert flag, f"没有找到-首页banner点击埋点校验V2: platform={platform},page_key={page_key},mod_nm=cm_main_banner,co中没有空参数的埋点"


    @allure.step("首页banner_line点击埋点校验V2: platform={platform},page_key={page_key},mod_nm=cm_main_banner_line")
    @pytest.mark.parametrize("platform, page_key", platform_and_page_key)
    def test_0030000_home_track_t2_click_banner_line(self, platform, page_key, clickhouse_client, time_range):
        sql = f"""
                select JSONExtractRaw(params, 'mod_nm') as a,  JSONExtractRaw(JSONExtractRaw(params, 'co'), 'target_type') as b, JSONExtractRaw(params, 'co') as c from weee_data.data_tracking_all where __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_page_key='{page_key}' and a='"cm_banner_line"' and b='"banner_line"' limit 100
            """
        log.info("sql===> " + sql)
        result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
        log.info("length===> " + str(len(result_rows)))
        assert result_rows, f"没有找到-首页banner_line点击埋点校验V2: platform={platform},page_key={page_key},mod_nm=cm_main_banner_line的埋点"
        new_json_list = [json.loads(item[2]) for item in result_rows if item[2].startswith("{")]
        flag = False
        for j in new_json_list:
            if TrackCommonVerify.is_null_in_dict(j):
                flag = True
                break
        assert flag, f"没有找到-首页banner_line点击埋点校验V2: platform={platform},page_key={page_key},mod_nm=cm_main_banner_line,co中没有空参数的埋点"

    module_name = ["cm_item_new",
                   "cm_item_trending",
                   "cm_item_sale",
                   "cm_lightning_deals",
                   "cm_product_line_tabs_fresh_daily"]



    @allure.step("首页normal_button点击埋点校验V2: platform={platform},page_key={page_key},mod_nm={mod_nm}")
    @pytest.mark.parametrize("platform, page_key", platform_and_page_key)
    @pytest.mark.parametrize("mod_nm", module_name)
    def test_0040000_home_track_t2_normal_button(self, platform, page_key, mod_nm, clickhouse_client, time_range):
        sql = f"""
                    select JSONExtractRaw(params, 'mod_nm') as a,  JSONExtractRaw(JSONExtractRaw(params, 'co'), 'target_nm') as b from weee_data.data_tracking_all where __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_page_key='{page_key}' and a='"{mod_nm}"' and b='"explore_more"' limit 100
                """
        log.info("sql===> " + sql)
        result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
        log.info("length===> " + str(len(result_rows)))
        assert result_rows, f"未检查到首页normal_button, platform={platform}, page_key={page_key}, mod_nm={mod_nm}的normal button埋点"


    @allure.step("首页category_icon点击埋点校验V2: platform={platform}, page_key={page_key}, mod_nm=cm_categories")
    @pytest.mark.parametrize("platform, page_key", platform_and_page_key)
    def test_0050000_home_track_t2_click_category_icon(self, platform, page_key, clickhouse_client, time_range):
        sql = f"""
                         select JSONExtractRaw(params, 'mod_nm') as a,  JSONExtractRaw(JSONExtractRaw(params, 'co'), 'target_type') as b, JSONExtractRaw(params, 'co') as c from weee_data.data_tracking_all where __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_page_key='{page_key}' and a='"cm_categories"' and b='"category"' limit 100
                     """
        log.info("sql===> " + sql)
        result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
        log.info("length===> " + str(len(result_rows)))
        assert result_rows, f"没有找到-首页category_icon点击埋点校验V2: platform={platform}, page_key={page_key}, mod_nm=cm_categories的埋点"
        new_json_list = [json.loads(item[2]) for item in result_rows if item[2].startswith("{")]
        flag = False
        for j in new_json_list:
            if TrackCommonVerify.is_null_in_dict(j):
                flag = True
        assert flag, f"未找到platform={platform},page_key={page_key},mod_nm=cm_categories, 对应的target_type==category, co参数中没有为空参数的埋点"

    # def test_006_home_track_t2_click_waterfall(self, platform, page_key, track):
    #     print("not implemented yet!")


    @allure.step("首页banner点击埋点校验V2: platform={platform}, page_key={page_key}, mod_nm=cm_main_banner")
    @pytest.mark.parametrize("platform, page_key", platform_and_page_key)
    def test_0070000_home_track_t2_banner_imp_banner(self, platform, page_key, clickhouse_client, time_range):
        sql = f"""
                      select JSONExtractRaw(params, 'mod_nm') as a,  JSONExtractRaw(JSONExtractRaw(params, 'co'), 'banner_type') as b, JSONExtractRaw(params, 'co') as c from weee_data.data_tracking_all where __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_page_key='{page_key}' and a='"cm_main_banner"' and b='"home_top_banner"' limit 100
                  """
        log.info("sql===> " + sql)
        result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
        log.info("length===> " + str(len(result_rows)))
        assert result_rows, f"没有找到-首页banner点击埋点校验V2: platform={platform}, page_key={page_key}, mod_nm=cm_main_banner的埋点"
        new_json_list = [json.loads(item[2]) for item in result_rows if item[2].startswith("{")]
        flag = False
        for j in new_json_list:
            if TrackCommonVerify.is_null_in_dict(j):
                flag = True
                break
        assert flag, f"未找到platform={platform},page_key={page_key},mod_nm=cm_main_banner, 对应的banner_type =home_top_banner, co参数中没有为空参数的埋点"

    t2_cart_action_add_to_cart_and_product_imp_mod_nm = [
        "cm_item_editors_pick",
        "cm_item_sale",
        "cm_item_new",
        "cm_item_trending",
        "cm_item_sale",
        "cm_lightning_deals",
        "cm_product_line_tabs_fresh_daily",
        "cm_mkpl_lightning",
        "cm_content_feed",
        "cm_collection_v2_manual_rank2",
        "cm_item_ads_collection"
    ]


    @allure.step("首页add_to_cart埋点校验V2: platform={platform},page_key={page_key},mod_nm={mod_nm}")
    @pytest.mark.parametrize("platform, page_key", platform_and_page_key)
    @pytest.mark.parametrize("mod_nm", t2_cart_action_add_to_cart_and_product_imp_mod_nm)
    def test_0080000_home_track_t2_cart_action_add_to_cart(self, platform, page_key, mod_nm, clickhouse_client,
                                                           time_range):
        sql = f"""
                  select JSONExtractRaw(params, 'mod_nm') as a,  JSONExtractRaw(JSONExtractRaw(params, 'co'), 'source') as b, JSONExtractRaw(params, 'sec_nm') as c from weee_data.data_tracking_all where __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_page_key='{page_key}' and a='"{mod_nm}"' and b='"{page_key}-{mod_nm}-null"' limit 100
              """
        log.info("sql===> " + sql)
        result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
        log.info("length===> " + str(len(result_rows)))
        assert result_rows, f"没有找到-首页add_to_cart埋点校验V2: platform={platform},page_key={page_key},mod_nm={mod_nm}的埋点"
        if mod_nm in ["cm_collection_v2_manual_rank1", "cm_collection_v2_manual_rank2"]:
            for j in result_rows:
                assert j[2] and j[
                    2] != "", f"未检查到platform={platform}, page_key={page_key}, mod_nm={mod_nm}, source={page_key}-{mod_nm}-null的add_to_cart埋点"


    @allure.step("首页t2_prod_imp产品曝光埋点校验V2: platform={platform},page_key={page_key},mod_nm={mod_nm}")
    @pytest.mark.parametrize("platform, page_key", platform_and_page_key)
    @pytest.mark.parametrize("mod_nm", t2_cart_action_add_to_cart_and_product_imp_mod_nm)
    def test_0090000_home_track_t2_prod_impose(self, platform, page_key, mod_nm,  clickhouse_client, time_range):
        sql = f"""
                  select JSONExtractRaw(params, 'mod_nm') as a, JSONExtractRaw(params, 'co') as b, JSONExtractRaw(params, 'sec_nm') as c from weee_data.data_tracking_all where __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_page_key='{page_key}' and a='"{mod_nm}"' limit 100
              """
        log.info("sql===> " + sql)
        result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
        log.info("length===> " + str(len(result_rows)))
        assert result_rows, f"没有找到-t2_prod_imp产品曝光埋点校验V2: platform={platform},page_key={page_key},mod_nm={mod_nm}的埋点"
        new_json_list = [json.loads(item[1]) for item in result_rows if item[1].startswith("{")]
        flag = False
        for j in new_json_list:
            if TrackCommonVerify.is_null_in_dict(j):
                flag = True
        assert flag, f"未找到t2_prod_imp产品曝光埋点校验V2: platform={platform},page_key={page_key},mod_nm={mod_nm}, co参数中没有为空参数的埋点"

        if mod_nm in ["cm_item_ads_collection", "cm_collection_v2_manual_rank2"]:
            for j in result_rows:
                assert j[2] and j[2] != "", f"未检查到platform={platform}, page_key={page_key}, mod_nm={mod_nm}的t2_prod_imp埋点"



    @allure.step(
        "首页分类标签曝光t2_ellipse_imp埋点校验V2: platform={platform}, page_key={page_key}, mod_nm=cm_categories")
    @pytest.mark.parametrize("platform, page_key", platform_and_page_key)
    def test_0100000_home_track_t2_ellipse(self, platform, page_key, clickhouse_client, time_range):
        sql = f"""
                  select JSONExtractRaw(params, 'mod_nm') as a, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'ellipse_type') as b from weee_data.data_tracking_all where __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_page_key='{page_key}' and a='"cm_categories"' and b='"category"' limit 100
              """

        log.info("sql===> " + sql)
        result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
        log.info("length===> " + str(len(result_rows)))
        assert result_rows, f"没有找到-首页platform={platform},page_key={page_key},mod_nm=cm_categories, 对应的ellipse_type=category的埋点"
