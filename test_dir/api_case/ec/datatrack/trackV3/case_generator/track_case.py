class TrackCase:
    def __init__(self, data):
        self.data = data

    def case_data_compose(self):
        pass

    def case_generate_common(self, list_data):
        where = []
        assertion = []
        sql = []
        base_sql = """ select params from weee_data.data_tracking_all where """
        for i in list_data:
            for k, v in i:
                if v[1] == '查询':
                    where.append(f"{k}={v[0]}")
                elif v[1] == '断言':
                    if v[0] == 'null':
                        assertion.append(f"assert {k} is None")
                    elif v[0] == 'not null':
                        assertion.append(f"assert {k} is not None")
                    else:
                        assertion.append(f"assert {k} == {v[0]}")
            sql.append(base_sql + " and ".join(where))

    def case_generate(self):
        pass

