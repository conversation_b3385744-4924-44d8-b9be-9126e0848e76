import pandas as pd
import numpy as np
from weeeTest import log

from test_dir.api_case.ec.common.get_root_dir import get_project_dir


class ReadExcel:
    def __init__(self, sheet_name):
        self.sheet_name = sheet_name
        self._path = get_project_dir()

    def read(self):
        """
        读取/test_data/ec/scene下埋点excel文件，返回所有数据
        """
        # df =  pd.read_excel(self._path + "/test_data/ec/scene/TrackingMonitorAutomationProgram.xlsx", sheet_name=self.sheet_name).replace(np., "blank")
        df =  pd.read_excel(self._path + "/test_data/ec/scene/TrackingMonitorAutomationProgram.xlsx", sheet_name=self.sheet_name, keep_default_na=False).replace('', "blank")
        return df.values, df.columns.values

class ReadCsv:
    def __init__(self, csv_file_name):
        self._path = get_project_dir()
        self.csv_file_name = csv_file_name

    def read(self):
        """
        读取/test_data/ec/scene下埋点csv文件，根据返回所有数据
        """
        try:
            df = pd.read_csv(self._path + f"/test_data/ec/scene/track_config_csv/{self.csv_file_name}", keep_default_na=False).replace('', "blank")
            return df.values, df.columns.values
        except UnicodeDecodeError as e:
            log.info("当前CSV文件的编码不是utf-8" + str(e))
            df = pd.read_csv(self._path + f"/test_data/ec/scene/track_config_csv/{self.csv_file_name}", keep_default_na=False, encoding="GBK").replace('', "blank")
            return df.values, df.columns.values
        




if __name__ == '__main__':
    # p = get_project_dir()
    # r = ReadExcel("t2_click_action").read()

    r = ReadCsv("t2_click_action.csv").read()
    print(1)