import json

import allure
import pytest
import requests
import weeeTest
from pytest_assume.plugin import assume
from allure import step
from weeeTest import log

from test_dir.api_case.ec.datatrack.base_case import BaseCase
from test_dir.api_case.ec.datatrack.track_configuration import *


class TestTrackAnalysis(weeeTest.TestCase, BaseCase):

    # with open("../configuration.json", "r", encoding="UTF-8") as fp:
    #     all_case_list = json.load(fp)
    #
    # print(all_case_list)
    # @allure.step("埋点-{all_list}")
    @pytest.mark.parametrize("all_list", checkout_case_list)
    @pytest.mark.dynamic
    def test_88880000_dynamic(self, all_list, clickhouse_client, time_range):
        print(all_list)
        for pp in all_list.get('platform_page_key'):
            where = f"""__time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{pp[0]}' and l0_page_key='{pp[1]}' """
            if type(all_list.get("click_type")) == list and type(all_list.get("components")) == list:
                # 如果既带有components参数，又带有click_type参数，此分支被覆盖
                for ct in all_list.get("click_type"):
                    ct_where = where + f""" and JSONExtractRaw(JSONExtractRaw(params, 'co'), 'click_type')='"{ct}"'"""
                    for component in all_list.get("components"):
                        component_where = ct_where + f""" and JSONExtractRaw(params, 'mod_nm')='"{component}"'"""
                        sql = all_list.get("sql") + " where " + component_where + " and " + all_list.get("where")
                        log.info("sql===> " + sql)
                        result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows

                        if isinstance(result_rows, list):
                            with assume, step(f"当前埋点-{all_list.get('desc')}-platform={pp[0]}, page_key={pp[1]}, component={component},click_type={ct}"):
                                assert result_rows, f"未找到当前买点-{all_list.get('desc')}-platform={pp[0]}, page_key={pp[1]}, component={component},click_type={ct}"
                                log.info("length===> " + str(len(result_rows)))
                        else:
                            with assume, step(f"当前埋点-{all_list.get('desc')}-platform={pp[0]}, page_key={pp[1]}, component={component},click_type={ct}"):
                                assert False, f"未找到当前买点-{all_list.get('desc')}-platform={pp[0]}, page_key={pp[1]}, component={component},click_type={ct}查询异常，请检查SQL语句是否正确或clickhouse连接"

            elif type(all_list.get("click_type")) == list and type(all_list.get("components")) != list:
                for ct in all_list.get("click_type"):
                    ct_where = where + f""" and JSONExtractRaw(JSONExtractRaw(params, 'co'), 'click_type')='"{ct}"'"""
                    sql = all_list.get("sql") + " where " + ct_where + " and " + all_list.get("where")
                    log.info("sql===> " + sql)
                    result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows

                    if isinstance(result_rows, list):
                        with assume, step(f"当前埋点-{all_list.get('desc')}-platform={pp[0]}, page_key={pp[1]}, click_type={ct}"):
                            assert result_rows, f"未找到当前买点-{all_list.get('desc')}-platform={pp[0]}, page_key={pp[1]}, click_type={ct}"
                            log.info("length===> " + str(len(result_rows)))
                    else:
                        with assume, step(f"当前埋点-{all_list.get('desc')}-platform={pp[0]}, page_key={pp[1]}, click_type={ct}"):
                            assert False, f"未找到当前买点-{all_list.get('desc')}-platform={pp[0]}, page_key={pp[1]}, click_type={ct}查询异常，请检查SQL语句是否正确或clickhouse连接"

            elif type(all_list.get("click_type")) != list and type(all_list.get("components")) == list:
                for component in all_list.get("components"):
                    component_where = where + f""" and JSONExtractRaw(params, 'mod_nm')='"{component}"'"""
                    sql = all_list.get("sql") + " where " + component_where + " and " + all_list.get("where")
                    log.info("sql===> " + sql)
                    res_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
                    if isinstance(res_rows, list):
                        with assume, step(f"当前埋点-{all_list.get('desc')}-platform={pp[0]}, page_key={pp[1]}, component={component}"):
                            assert res_rows, f"未找到当前买点-{all_list.get('desc')}-platform={pp[0]}, page_key={pp[1]}, component={component}"
                            log.info("length===> " + str(len(res_rows)))
                    else:
                        with assume, step(f"当前埋点-{all_list.get('desc')}-platform={pp[0]}, page_key={pp[1]}, component={component}"):
                            assert False, f"未找到当前买点-{all_list.get('desc')}-platform={pp[0]}, page_key={pp[1]}, component={component}查询异常，请检查SQL语句是否正确或clickhouse连接"

            else:
                sql = all_list.get("sql") + " where " + where + " and " + all_list.get("where")
                log.info("sql===> " + sql)
                result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
                if isinstance(result_rows, list):
                    with assume, step(f"当前埋点-{all_list.get('desc')}-platform={pp[0]}, page_key={pp[1]}"):
                        assert result_rows, f"未找到当前买点-{all_list.get('desc')}-platform={pp[0]}, page_key={pp[1]}"
                        log.info("length===> " + str(len(result_rows)))
                else:
                    with assume, step(f"当前埋点-{all_list.get('desc')}-platform={pp[0]}, page_key={pp[1]}"):
                        assert False, f"当前买点-{all_list.get('desc')}-platform={pp[0]}, page_key={pp[1]}查询异常，请检查SQL语句是否正确或clickhouse连接"

    def test_clickhouse_data(self, clickhouse_client, time_range):
        sql = f"""
            select params from  weee_data.data_tracking_all where __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='iOS' limit 10000
        """
        log.info("sql===> " + sql)
        result_rows = self.query_track_generator(client=clickhouse_client, sql=sql)
        i = 1
        j = 1
        for item in result_rows:
            log.info("i===>" + str(i) + " j===>" + str(j))
            log.info("item===>" + str(item))


    def test_88880001_track_data_order_count_analysis(self, clickhouse_client, analysis_time_scope, time_range):
        """
        计算重要埋点分时数量
        """
        base_sql = f"""
                        select count(*) from weee_data.data_tracking_all where l0_event_type='t2_order_confirmation' and
                    """

        _date = analysis_time_scope[0].get("begin").split(" ")[0]
        # excel表格地址： https://docs.google.com/spreadsheets/d/1qVVJefG_OPz6iGTnDbOrmDdcVlV9DARZZ0PMATLu9gc/edit?usp=sharing
        # 1. 核心页面之home page--> page view excel表格第3行
        for platform, page_key in [("MWeb", "mweb_home"),("Android", "android_home"),("iOS", "ios_home"),("DWeb", "dweb_home"),
                                   ("MWeb", "mweb_category"), ("Android", "android_category"), ("iOS", "ios_category"), ("DWeb", "dweb_category"),
                                   ("MWeb", "mweb_product"), ("Android", "android_product"), ("iOS", "ios_product"), ("DWeb", "dweb_product"),
                                   ("MWeb", "mweb_cart"), ("Android", "android_cart"), ("iOS", "ios_cart"), ("DWeb", "dweb_cart"),
                                   ("MWeb", "mweb_search_result"), ("Android", "android_search_result"), ("iOS", "ios_search_result"), ("DWeb", "dweb_search_result"),
                                   ("MWeb", "mweb_collection"), ("Android", "android_collection"), ("iOS", "ios_collection"), ("DWeb", "dweb_collection")
                                   ]:
            for item_page_view in analysis_time_scope:
                sql = f"""select count(*) from weee_data.data_tracking_all where  __time>='{item_page_view.get("begin")}' and __time<='{item_page_view.get("end")}' and l0_platform='{platform}' and l0_event_type='page_view' and l0_page_key='{page_key}' """
                log.info("sql===> " + sql)
                rows = self._analysis_track(clickhouse_client=clickhouse_client, sql=sql)
                with open(f"{_date}_home_page_view_3.txt", mode='a+', encoding='UTF-8') as fp:
                    fp.write(platform + " " + page_key + " page_view" + " " + json.dumps(item_page_view) + json.dumps(rows) + "\n")

        # 2 首页banner excel表格第4行
        for platform_2, page_key_2 in [("MWeb", "mweb_home"),("Android", "android_home"),("iOS", "ios_home"),("DWeb", "dweb_home")]:
            for item_banner in analysis_time_scope:
                sql_2 = f""" select count(1) from weee_data.data_tracking_all where  __time>='{item_banner.get("begin")}' and __time<='{item_banner.get("end")}' and l0_platform='{platform_2}' and l0_page_key='{page_key_2}' and l0_event_type='t2_click_action' and JSONExtractRaw(params, 'mod_nm')='"cm_main_banner"' and JSONExtractRaw(JSONExtractRaw(params, 'co'), 'target_type')='"home_top_banner"' """
                log.info("sql2===> " + sql_2)
                rows_2 = self._analysis_track(clickhouse_client=clickhouse_client, sql=sql_2)
                with open(f"{_date}_home_banner_4.txt", mode='a+', encoding='UTF-8') as fp:
                    fp.write(platform_2 + " " + page_key_2 + " t2_click_action" + " " + json.dumps(item_banner) + json.dumps(rows_2) + "\n")

        # 3. 横向组件 excel表格第5行
        for platform_3, page_key_3 in [("MWeb", "mweb_home"), ("Android", "android_home"), ("iOS", "ios_home"), ("DWeb", "dweb_home")]:
            for item_3 in analysis_time_scope:
                sql_3 = f""" select count(1) from weee_data.data_tracking_all where  __time>='{item_3.get("begin")}' and __time<='{item_3.get("end")}' and l0_platform='{platform_3}' and l0_page_key='{page_key_3}' and l0_event_type='t2_cart_action' and JSONExtractRaw(params, 'mod_nm')='"cm_item_sale"' and JSONExtractRaw(JSONExtractRaw(params, 'co'), 'prod_id')!='""' """
                log.info("sql3===> " + sql_3)
                rows_3 = self._analysis_track(clickhouse_client=clickhouse_client, sql=sql_3)
                with open(f"{_date}_horizontal_component_5.txt", mode='a+', encoding='UTF-8') as fp:
                    fp.write(platform_3 + " " + page_key_3 + " t2_cart_action" + " " + json.dumps(item_3) + json.dumps(rows_3) + "\n")

        # 4. 分类->分类商品卡片 excel表格第6行
        for platform_4, page_key_4 in [("MWeb", "mweb_category"), ("Android", "android_category"), ("iOS", "ios_category"), ("DWeb", "dweb_category")]:
            for item_4 in analysis_time_scope:
                sql_4 = f""" select count(1) from weee_data.data_tracking_all where  __time>='{item_4.get("begin")}' and __time<='{item_4.get("end")}' and l0_platform='{platform_4}' and l0_page_key='{page_key_4}' and l0_event_type='t2_cart_action' and JSONExtractRaw(params, 'mod_nm')='"item_list"' and JSONExtractRaw(JSONExtractRaw(params, 'co'), 'prod_id')!='""' """
                log.info("sql4===> " + sql_4)
                rows_4 = self._analysis_track(clickhouse_client=clickhouse_client, sql=sql_4)
                with open(f"{_date}_category_6.txt", mode='a+', encoding='UTF-8') as fp:
                    fp.write(platform_4 + " " + page_key_4 + " t2_cart_action" + " " + json.dumps(item_4) + json.dumps(rows_4) + "\n")

        # 5. PDP->产品详情 excel表格第7行
        for platform_5, page_key_5 in [("MWeb", "mweb_product"), ("Android", "android_product"), ("iOS", "ios_product"), ("DWeb", "dweb_product")]:
            for item_5 in analysis_time_scope:
                sql_5 = f""" select count(1) from weee_data.data_tracking_all where  __time>='{item_5.get("begin")}' and __time<='{item_5.get("end")}' and l0_platform='{platform_5}' and l0_page_key='{page_key_5}' and l0_event_type='t2_cart_action' and JSONExtractRaw(params, 'mod_nm')='"product_detail"' and JSONExtractRaw(JSONExtractRaw(params, 'co'), 'prod_id')!='""' """
                log.info("sql5===> " + sql_5)
                rows_5 = self._analysis_track(clickhouse_client=clickhouse_client, sql=sql_5)
                with open(f"{_date}_pdp_7.txt", mode='a+', encoding='UTF-8') as fp:
                    fp.write(platform_5 + " " + page_key_5 + " t2_cart_action" + " " + json.dumps(item_5) + json.dumps(rows_5) + "\n")


        # 6. MKPL->waterfall页面 excel表格第8行
        for platform_6, page_key_6 in [("MWeb", "mweb_mkpl_waterfall"), ("Android", "android_mkpl_waterfall"), ("iOS", "ios_mkpl_waterfall"), ("DWeb", "dweb_mkpl_waterfall")]:
            for item_6 in analysis_time_scope:
                sql_6 = f""" select count(1) from weee_data.data_tracking_all where  __time>='{item_6.get("begin")}' and __time<='{item_6.get("end")}' and l0_platform='{platform_6}' and l0_page_key='{page_key_6}' and l0_event_type='t2_cart_action' and JSONExtractRaw(params, 'mod_nm')='"cm_content_feed_v2"' and JSONExtractRaw(JSONExtractRaw(params, 'co'), 'prod_id')!='""' """
                log.info("sql6===> " + sql_6)
                rows_6 = self._analysis_track(clickhouse_client=clickhouse_client, sql=sql_6)
                with open(f"{_date}_mpkl_waterfall_8.txt", mode='a+', encoding='UTF-8') as fp:
                    fp.write(platform_6 + " " + page_key_6 + " t2_cart_action" + " " + json.dumps(item_6) + json.dumps(rows_6) + "\n")


        # 7. 购物车->购物车 excel表格第9行
        for platform_7, page_key_7 in [("MWeb", "mweb_cart"), ("Android", "android_cart"), ("iOS", "ios_cart"), ("DWeb", "dweb_cart")]:
            for item_7 in analysis_time_scope:
                sql_7 = f""" select count(1) from weee_data.data_tracking_all where  __time>='{item_7.get("begin")}' and __time<='{item_7.get("end")}' and l0_platform='{platform_7}' and l0_page_key='{page_key_7}' and l0_event_type='t2_cart_action' and JSONExtractRaw(params, 'mod_nm')='"cart"' and JSONExtractRaw(JSONExtractRaw(params, 'co'), 'prod_id')!='""' """
                log.info("sql7===> " + sql_7)
                rows_7 = self._analysis_track(clickhouse_client=clickhouse_client, sql=sql_7)
                with open(f"{_date}_cart_9.txt", mode='a+', encoding='UTF-8') as fp:
                    fp.write(platform_7 + " " + page_key_7 + " t2_cart_action" + " " + json.dumps(item_7) + json.dumps(rows_7) + "\n")



        # 8. 购物车->checkout excel表格第10行
        for platform_8, page_key_8 in [("MWeb", "mweb_cart"), ("Android", "android_cart"), ("iOS", "ios_cart"), ("DWeb", "dweb_cart")]:
            for item_8 in analysis_time_scope:
                sql_8 = f""" select count(1) from weee_data.data_tracking_all where  __time>='{item_8.get("begin")}' and __time<='{item_8.get("end")}' and l0_platform='{platform_8}' and l0_page_key='{page_key_8}' and l0_event_type='t2_click_action'  and JSONExtractRaw(JSONExtractRaw(params, 'co'), 'target_type')='"normal_button"' and JSONExtractRaw(JSONExtractRaw(params, 'co'), 'target_nm')='"checkout"' """
                log.info("sql8===> " + sql_8)
                rows_8= self._analysis_track(clickhouse_client=clickhouse_client, sql=sql_8)
                with open(f"{_date}_cart_checkout_10.txt", mode='a+', encoding='UTF-8') as fp:
                    fp.write(platform_8 + " " + page_key_8 + " t2_cart_action" + " " + json.dumps(item_8) + json.dumps(rows_8) + "\n")


        # 9. 结算->订单成功页 excel表格第11行
        for platform_9, page_key_9 in [("MWeb", "mweb_order_confirmation"), ("Android", "android_order_confirmation"), ("iOS", "ios_order_confirmation"), ("DWeb", "dweb_order_confirmation")]:
            for item_9 in analysis_time_scope:
                sql_9 = f""" select count(1) from weee_data.data_tracking_all where  __time>='{item_9.get("begin")}' and __time<='{item_9.get("end")}' and l0_platform='{platform_9}' and l0_page_key='{page_key_9}' and l0_event_type='t2_page_sec_imp'  and JSONExtractRaw(params, 'mod_nm')='"order_confirm_animation"' """
                log.info("sql9===> " + sql_9)
                rows_9 = self._analysis_track(clickhouse_client=clickhouse_client, sql=sql_9)
                with open(f"{_date}_order_confirmation_11.txt", mode='a+', encoding='UTF-8') as fp:
                    fp.write(platform_9 + " " + page_key_9 + " t2_cart_action" + " " + json.dumps(item_9) + json.dumps(rows_9) + "\n")


        # 1天24小时，每小时的订单量
        # for item in analysis_time_scope:
        #     sql = f"""select count(*) from weee_data.data_tracking_all where l0_event_type='t2_order_confirmation' and  __time>='{item.get("begin")}' and __time<='{item.get("end")}'"""
        #     log.info("sql===> " + sql)
        # rows = self._analysis_track(clickhouse_client=clickhouse_client, sql=sql)
        # with open("order_count95.txt", mode='a+', encoding='UTF-8') as fp:
        #     fp.write(json.dumps(item) + json.dumps(rows) + "\n")


        # 1天24小时，各平台的订单量
        # platform = ["MWeb","Android", "iOS", "DWeb"]
        # for p in platform:
        #     sql = f"""select count(*) from weee_data.data_tracking_all where l0_event_type='t2_order_confirmation' and  __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{p}' """
        #     log.info("sql===> " + sql)
            # rows = self._analysis_track(clickhouse_client=clickhouse_client, sql=sql)
            # with open("order_platform.txt", mode='a+', encoding='UTF-8') as fp:
            #     fp.write(json.dumps(time_range) + p + json.dumps(rows) + "\n")


    def _analysis_track(self, clickhouse_client, sql, *args, **kwargs):
        result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
        return result_rows










