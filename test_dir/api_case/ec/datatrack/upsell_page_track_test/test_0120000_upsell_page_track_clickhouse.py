import allure
import pytest
import weeeTest
from weeeTest import log

from test_dir.api_case.ec.datatrack.base_case import BaseCase



@allure.story("upsell页面埋点操作与校验V2")
class TestUpsellPageTrackV2(weeeTest.TestCase, BaseCase):
    # upsell页面

    pytestmark = [pytest.mark.upselltrack, pytest.mark.clickhouse, pytest.mark.secondpagelevel]
    platform_and_referer_page_key = [("MWeb", "mweb_cart", "mweb_before_you_checkout"), ("Android", "android_cart", "android_before_you_checkout"), ("iOS", "ios_cart", "ios_before_you_checkout")]

    mod_nm = [
        "checkout_fresh_bakery", "checkout_recently_viewed",
        "checkout_complete", "checkout_sale", "checkout_fresh_deli"
    ]

    @allure.step(
        "upsell购物车点击去结算弹出upsell页面,t2_prod_imp埋点校验: platform={platform},referer_page_key={referer_page_key},mod_nm={mod_nm}")
    @pytest.mark.parametrize("platform, referer_page_key, page_key", platform_and_referer_page_key)
    @pytest.mark.parametrize("mod_nm", mod_nm)
    def test_0120000_upsell_track_t2_prod_impose(self, platform, referer_page_key, page_key, mod_nm, clickhouse_client, time_range):
        sql = f"""
                  select l0_referer_page_key, params, JSONExtractRaw(params, 'mod_nm') as a from weee_data.data_tracking_all where  __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_referer_page_key='{referer_page_key}' and l0_page_key='{page_key}' and a='"{mod_nm}"' limit 100
               """
        log.info("sql===> " + sql)
        result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
        log.info("length===> " + str(len(result_rows)))

        assert result_rows, f"未找到-upsell购物车点击去结算弹出upsell页面,t2_prod_imp埋点校验: platform={platform},referer_page_key={referer_page_key},mod_nm={mod_nm}的埋点"


    platform_and_page_key = [("MWeb", "mweb_before_you_checkout"), ("Android", "android_before_you_checkout"), ("iOS", "ios_before_you_checkout")]

    @allure.step(
        "upsell点击搜藏/取消搜藏,t2_click_action埋点校验: platform={platform},page_key={page_key},mod_nm={mod_nm}")
    @pytest.mark.parametrize("platform, page_key", platform_and_page_key)
    @pytest.mark.parametrize("mod_nm", mod_nm)
    @pytest.mark.parametrize("click_type", ["save", "unsave"])
    def test_0120001_upsell_track_t2_click_action(self, platform, page_key, mod_nm, click_type, clickhouse_client, time_range):
        sql = f"""
                  select l0_page_key, params, JSONExtractRaw(params, 'mod_nm') as a, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'click_type') as b from weee_data.data_tracking_all where  __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_page_key='{page_key}' and a='"{mod_nm}"' and b='"{click_type}"' limit 100
               """
        log.info("sql===> " + sql)
        result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
        log.info("length===> " + str(len(result_rows)))

        assert result_rows, f"未找到-upsell点击搜藏/取消搜藏,t2_click_action埋点校验: platform={platform},page_key={page_key},mod_nm={mod_nm},click_type={click_type}的埋点"

    @allure.step(
        "upsell加购upsell 里的商品,t2_cart_action埋点校验: platform={platform},page_key={page_key},mod_nm={mod_nm}")
    @pytest.mark.parametrize("platform, page_key", platform_and_page_key)
    @pytest.mark.parametrize("mod_nm", mod_nm)
    def test_0120002_upsell_track_t2_cart_action(self, platform, page_key, mod_nm, clickhouse_client, time_range):
        sql = f"""
                  select l0_page_key, params, JSONExtractRaw(params, 'mod_nm') as a, JSONExtractRaw(JSONExtractRaw(params, 'co'), 'source') as b from weee_data.data_tracking_all where  __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_page_key='{page_key}' and l0_page_key='{page_key}' and a='"{mod_nm}"' and b!='""' and b!='null' limit 100
               """
        log.info("sql===> " + sql)
        result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
        log.info("length===> " + str(len(result_rows)))

        assert result_rows, f"未找到-upsell加购upsell 里的商品,t2_cart_action埋点校验: platform={platform},page_key={page_key},mod_nm={mod_nm}的埋点"
