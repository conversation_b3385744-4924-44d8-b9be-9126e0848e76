import json

import allure
import pytest
import weeeTest
from weeeTest import log

from test_dir.api_case.ec.datatrack.base_case import BaseCase
from test_dir.api_case.ec.datatrack.collection_page_track_check_api.track_verification_collection_page_v2 import \
    TrackVerificationCollectionPageV2 as TVCPV2
from test_dir.api_case.ec.datatrack.datatrack_common_function import TrackCommonVerify


@allure.story("Collection页面埋点操作与校验V2")
class TestCollectionPageTrackV2(weeeTest.TestCase, BaseCase):
    # collection页面
    pytestmark = [pytest.mark.clickhouse, pytest.mark.secondpagelevel]
    platform_and_page_key = [("MWeb", "mweb_collection")]

    @allure.step(
        "collection页面点击产品卡片进入pdp|点击收藏|取消收藏t2_click_action埋点校验V2: platform={platform},page_key={page_key},target_type=product,click_type={click_type}")
    @pytest.mark.parametrize("platform, page_key", platform_and_page_key)
    @pytest.mark.parametrize("click_type", ["view", "save", "unsave"])
    def test_4000000_collection_track_t2_click_action(self, platform, page_key, click_type, clickhouse_client, time_range):
        # 此用例有可能失败，上次查询unsave和save，各只有一条数据
        # h5 save、unsave 无数据
        sql = f"""
                  select JSONExtractRaw(params, 'co') as a, JSONExtractRaw(params, 'ctx') as b from weee_data.data_tracking_all where  JSONExtractRaw(a, 'target_type')='"product"' and JSONExtractRaw(a, 'click_type')='"{click_type}"' and JSONExtractRaw(b, 'page_target')!='null' and JSONExtractRaw(b, 'page_target')!='' and  __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_page_key='{page_key}' limit 100
               """
        log.info("sql===> " + sql)
        result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
        log.info("length===> " + str(len(result_rows)))

        assert result_rows, f"collection页面点击产品卡片进入pdp|点击收藏|取消收藏t2_click_action埋点校验V2: platform={platform},page_key={page_key},target_type=product,click_type={click_type}"

    @allure.step(
        "collection页面点击分享按钮t2_click_action埋点校验V2: platform={platform},page_key={page_key},target_type=collection,click_type=share")
    @pytest.mark.parametrize("platform, page_key", platform_and_page_key)
    def test_4010000_collection_track_t2_click_action_share(self, platform, page_key, clickhouse_client, time_range):
        sql = f"""
                  select JSONExtractRaw(params, 'co') as a, JSONExtractRaw(params, 'ctx') as b from weee_data.data_tracking_all where  JSONExtractRaw(a, 'target_type')='"collection"' and JSONExtractRaw(a, 'click_type')='"share"' and JSONExtractRaw(b, 'page_target')!='null' and JSONExtractRaw(b, 'page_target')!='' and  __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_page_key='{page_key}' limit 100
               """
        log.info("sql===> " + sql)
        result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
        log.info("length===> " + str(len(result_rows)))

        assert result_rows, f"collection页面点击分享按钮t2_click_action埋点校验V2: platform={platform},page_key={page_key},target_type=collection,click_type=share"

    @allure.step(
        "collection页面产品加购t2_cart_action埋点校验V2: platform={platform},page_key={page_key},mod_nm=null")
    @pytest.mark.parametrize("platform, page_key", platform_and_page_key)
    def test_4020000_collection_track_t2_cart_action(self, platform, page_key, clickhouse_client, time_range):
        sql = f"""
                  select JSONExtractRaw(params, 'co') as a, JSONExtractRaw(params, 'ctx') as b, JSONExtractRaw(JSONExtractRaw(params, 'ctx'), 'page_target') as c from weee_data.data_tracking_all where  JSONExtractRaw(params, 'mod_nm')='null'  and  __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_page_key='{page_key}' limit 100
               """
        log.info("sql===> " + sql)
        result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
        log.info("length===> " + str(len(result_rows)))

        # source={page_key}-null-{page_target} sql语句无法实现，自行组装
        new_result = []
        for i in result_rows:
            json_co = json.loads(i[0]) if "source" in i[0] else None
            page_target = str.replace(i[2], '"', '') if i[2] else None

            if json_co and page_target and json_co.get('source') and json_co.get('source') == f'{page_key}-null-{page_target}':
                new_result.append(i)

        assert new_result, f"collection页面产品加购t2_cart_action埋点校验V2: platform={platform},page_key={page_key},mod_nm=null"

    @allure.step(
        "collection页面产品imp,t2_prod_imp埋点校验V2: platform={platform},page_key={page_key},mod_nm=null")
    @pytest.mark.parametrize("platform, page_key", platform_and_page_key)
    def test_4030000_collection_track_t2_prod_imp(self, platform, page_key, clickhouse_client, time_range):
        sql = f"""
                  select JSONExtractRaw(params, 'co') as a, JSONExtractRaw(params, 'mod_nm') as b, JSONExtractRaw(JSONExtractRaw(params, 'ctx'), 'page_target') as c from weee_data.data_tracking_all where  __time>='{time_range.get("begin")}' and __time<='{time_range.get("end")}' and l0_platform='{platform}' and l0_page_key='{page_key}' and b='null' and c!='' and c!='null' limit 100
               """
        log.info("sql===> " + sql)
        result_rows = self.query_track(client=clickhouse_client, sql=sql).result_rows
        log.info("length===> " + str(len(result_rows)))

        co_flag = True
        co_json = [json.loads(item[0]) for item in result_rows if item[0].startswith("{")]
        for co in co_json:
            if not TrackCommonVerify.is_null_in_dict(co):
                co_flag = False

        assert co_flag, f"collection页面产品imp,t2_prod_imp埋点校验V2: platform={platform},page_key={page_key},mod_nm=null"


