# !/usr/bin/python3
# -*- coding: utf-8 -*-


import weeeTest

from test_dir.api.ec.central_portal.central_im import CentralIm


class TestSalesTopProductsConfig(weeeTest.TestCase):

    @weeeTest.mark.list('sales', 'Transaction', 'test_sales_synonyms')
    def test_sales_top_products_config(self, sales_header):
        """ # 商品销售信息管理-商品内容信息管理-置顶配置-查询置顶配置页面数据"""
        scene_list = CentralIm().scene_list(headers=sales_header, langs=["en"], stores=["cn"],
                                            pages=["Home"], sales_org_ids=[4])
        assert len(scene_list["object"]) > 0, f'查询置顶配置页面数据异常{scene_list}'
        for item in scene_list["object"]:
            scene_product_list = CentralIm().scene_product_list(headers=sales_header, scene_id=item["scene_id"])
            assert scene_product_list["result"] is True, f'查询置顶配置页面数据异常{scene_product_list}'
