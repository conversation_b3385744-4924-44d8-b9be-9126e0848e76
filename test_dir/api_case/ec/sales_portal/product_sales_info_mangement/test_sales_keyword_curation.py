# !/usr/bin/python3
# -*- coding: utf-8 -*-


import weeeTest

from test_dir.api.ec.central_portal.central_im import CentralIm


class TestSalesKeywordCuration(weeeTest.TestCase):

    @weeeTest.mark.list('sales', 'Transaction', 'test_sales_synonyms')
    def test_sales_keyword_curation(self, sales_header):
        """ # 商品销售信息管理-商品内容信息管理-关键词管理-查询同关键词管理页面数据"""
        keyword_top_list = CentralIm().search_keyword_top_list(headers=sales_header)

        assert len(keyword_top_list["object"]["data"]) > 0, f'查询同关键词管理页面数据异常{keyword_top_list}'

