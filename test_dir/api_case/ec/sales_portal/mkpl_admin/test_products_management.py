# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
sales portal MKPL 商品管理模块
"""
import weeeTest
from weeeTest import jmespath

from test_dir.api.ec.central_portal.central_mkpl import ProductsMgmt, Filters
from . import sales_header
from weeeTest import weeeConfig


class TestNewProductReview(weeeTest.TestCase):
    """
    商品审核
    """

    @weeeTest.mark.list('sales', 'Transaction', 'MKPL', 'test_products_management')
    def test_product_audit_records_global_plus(self):
        """
        sales portal MKPL商品审核Global+ Self Ship tab 列表
        """
        # Global+审核列表的 All Items
        all_items_list = ProductsMgmt().audit_records(headers=sales_header, biz_type="seller")
        # Global+审核列表各个tab下的商品数量
        count = ProductsMgmt().product_audit_count(headers=sales_header, biz_type="seller")

        # 断言审核列表返回all items数量与统计数量一致
        all_items_list_count = jmespath(all_items_list, "object.total")
        total_count = jmespath(count, "object.totalCount")
        assert all_items_list_count == total_count, f"The all items list_count {all_items_list_count} does not match the total count {total_count}"
        # 断言返回的商品都是seller类型的商品
        items = jmespath(all_items_list, "obeject.list")
        all_items_valid = all(item.get('bizType') == 'seller' for item in items)
        assert all_items_valid, "Not all items in the draft list have an audit_status of 'E' and bizType of 'seller'"

        for item in items:
            product_id = item.get('id')
            audit_status = item.get('audit_status')

            if audit_status == 'E':
                # 如果商品审核状态是 E，则判断审核进度是 0
                response = ProductsMgmt.product_audit_progress(headers=sales_header, product_id=product_id)
                current_progress = jmespath(response, "object.current_progress")
                assert current_progress == 0, f"Product ID {product_id} with audit status 'E' should have current progress 0 but has {current_progress}"
            elif audit_status in ['A', 'C']:
                # 如果审核状态是 A 或 C，则判断审核进度是 3
                response = ProductsMgmt.product_audit_progress(headers=sales_header, product_id=product_id)
                current_progress = jmespath(response, "object.current_progress")
                assert current_progress == 3, f"Product ID {product_id} with audit status '{audit_status}' should have current progress 3 but has {current_progress}"

        # 断言审核列表返回草稿状态的商品数量与统计数量一致
        draft_count = jmespath(count, "object.draftCount")
        draft_list = ProductsMgmt().audit_records(headers=sales_header, biz_type="seller", audit_status="E")
        draft_list_count = jmespath(draft_list, "object.total")
        assert draft_list_count == draft_count, f"The draft list count {draft_list_count} does not match the draft count {draft_count}"
        # 获取草稿商品列表
        items_e = jmespath(draft_list, "object.list")
        # 断言所有商品的 audit_status 是否为草稿 "E" 并且 bizType 是否为 "seller"
        e_items_valid = all(item.get('audit_status') == 'E' and item.get('bizType') == 'seller' for item in items_e)
        assert e_items_valid, "Not all items in the draft list have an audit_status of 'E' and bizType of 'seller'"

        # 断言审核列表返回pending状态的商品数量与统计数量一致
        pending_list = ProductsMgmt().audit_records(headers=sales_header, biz_type="seller", audit_status="A")
        pending_count = jmespath(count, "object.pendingCount")
        pending_list_count = jmespath(pending_list, "object.total")
        assert pending_list_count == pending_count, f"The pending  list count {pending_list_count} does not match the pending count {pending_count}"
        # 断言所有商品的 audit_status 是否为待审核 "A" 并且 bizType 是否为 "seller"
        items_a = jmespath(pending_list, "object.list")
        a_items_valid = all(item.get('audit_status') == 'A' and item.get('bizType') == 'seller' for item in items_a)
        assert a_items_valid, "Not all items in the pending list have an audit_status of 'A' and bizType of 'seller'"

        # 断言审核列表返回rejected状态的商品数量与统计数量一致
        rejected_list = ProductsMgmt().audit_records(headers=sales_header, biz_type="seller", audit_status="C")
        rejected_count = jmespath(count, "object.rejectedCount")
        rejected_list_count = jmespath(rejected_list, "object.total")
        assert rejected_list_count == rejected_count, f"The rejected items list_count {rejected_list_count} does not match the rejected count {rejected_count}"
        # 断言所有商品的 audit_status 是否为拒绝 "C" 并且 bizType 是否为 "seller"
        items_c = jmespath(rejected_list, "object.list")
        c_items_valid = all(item.get('audit_status') == 'C' and item.get('bizType') == 'seller' for item in items_c)
        assert c_items_valid, "Not all items in the rejected list have an audit_status of 'C' and bizType of 'seller'"

    @weeeTest.mark.list('sales', 'Transaction', 'MKPL', 'test_products_management')
    def test_product_audit_records_global_plus_filter(self):
        """
        sales portal MKPL商品审核Global+ Self Ship tab 列表条件筛选
        """

        # Global+审核列表的 filter查询Seller,Keywords,SKU ID,Audit status,Audit Progress,Update Time,To Do,BD
        vendor_id = jmespath(Filters().vendor_market_list(headers=sales_header, biz_type='seller'), 'object[0].id')
        BD_id = jmespath(Filters().account_manager_list(headers=sales_header), 'object[0].id')
        BD_name = jmespath(Filters().account_manager_list(headers=sales_header), 'object[0].name')
        res = ProductsMgmt().audit_records(headers=sales_header, biz_type="seller", vendor_id=vendor_id,
                                           account_manager_id=BD_id)
        if res['total'] > 0:
            assert int(jmespath(res, 'object.list[0].vendorId')) == int(vendor_id) \
                   and BD_name == jmespath(res,
                                           'object.list[0].bdName'), f"The items list does not match the filter  {vendor_id} and {BD_name}"

    @weeeTest.mark.list('sales', 'Transaction', 'MKPL', 'test_products_management')
    def test_product_audit_records_global_fbw(self):
        """
        sales portal MKPL商品审核Global+FBW tab 列表
        """

        # 获取总计数
        count = jmespath(ProductsMgmt().product_audit_count(headers=sales_header, biz_type="mkpl_fbw"), "object")
        # res = ProductsMgmt().product_audit_count(headers=sales_header, biz_type="mkpl_fbw")
        # count = res["object"]
        assert count, "Count is empty or None!"

        # 定义可能的 audit_status 值和对应的计数字段
        audit_status_to_count_field = {
            None: "totalCount",  # 当audit_status未传参时对应的字段
            'E': "draftCount",
            'C': "rejectedCount",
            'A': "pendingCount"
        }

        # 对每个 audit_status 进行断言
        for status in audit_status_to_count_field:
            # 获取特定 audit_status 的列表并断言内容不能为空
            mkpl_fbw_item_list = ProductsMgmt().audit_records(headers=sales_header, biz_type="mkpl_fbw",
                                                              audit_status=status)
            assert mkpl_fbw_item_list, f"Item list for audit_status '{status}' is empty or None!"

            # 提取列表中的总数
            item_list_total = jmespath(mkpl_fbw_item_list, "object.total")

            # 获取对应的计数值
            expected_count = count[audit_status_to_count_field[status]]

            # 断言列表的总数与预期计数值相等
            assert item_list_total == expected_count, f"For audit_status '{status}', the total {item_list_total} does not match the expected count {expected_count}"

            # 遍历商品列表进行断言
            for item in jmespath(mkpl_fbw_item_list, "object.list[]"):
                # 断言商品bizType为mkpl_fbw
                assert item[
                           'bizType'] == 'mkpl_fbw', f"Item {item['id']} has incorrect bizType {item['bizType']}, expected 'mkpl_fbw'"

                # 当传入status时，断言商品auditStatus与status一致
                if status is not None:
                    assert item[
                               'auditStatus'] == status, f"Item {item['id']} has incorrect auditStatus {item['auditStatus']}, expected '{status}'"

    @weeeTest.mark.list('sales', 'Transaction', 'MKPL', 'test_products_management')
    def test_product_audit_records_local_fbw(self):
        """
        sales portal MKPL商品审核Local FBW tab 列表
        """
        # 获取总计数
        count = jmespath(ProductsMgmt().product_audit_count(headers=sales_header, biz_type="fbw"), "object")

        # 定义可能的 audit_status 值和对应的计数字段
        audit_status_to_count_field = {
            None: "totalCount",  # 当audit_status未传参时对应的字段
            'E': "draftCount",
            'C': "rejectedCount",
            'A': "pendingCount"
        }

        # 对每个 audit_status 进行断言
        for status in audit_status_to_count_field:
            # 获取特定 audit_status 的列表
            mkpl_fbw_item_list = ProductsMgmt().audit_records(headers=sales_header, biz_type="fbw",
                                                              audit_status=status)
            # 提取列表中的总数
            item_list_total = jmespath(mkpl_fbw_item_list, "object.total")

            # 获取对应的计数值
            expected_count = count[audit_status_to_count_field[status]]

            # 断言列表的总数与预期计数值相等
            assert item_list_total == expected_count, f"For audit_status '{status}', the total {item_list_total} does not match the expected count {expected_count}"

            # 遍历商品列表进行断言
            for item in jmespath(mkpl_fbw_item_list, "object.list[]"):
                # 断言商品bizType为mkpl_fbw
                assert item[
                           'bizType'] == 'fbw', f"Item {item['id']} has incorrect bizType {item['bizType']}, expected 'fbw'"

                # 当传入status时，断言商品auditStatus与status一致
                if status is not None:
                    assert item[
                               'auditStatus'] == status, f"Item {item['id']} has incorrect auditStatus {item['auditStatus']}, expected '{status}'"


class TestNewProductsList(weeeTest.TestCase):
    """
    商品列表
    """

    @weeeTest.mark.list('sales', 'Transaction', 'MKPL', 'test_products_management')
    def test_global_plus_products_list(self):
        """
        商品列表 Global+ Self Ship SKU tab
        """
        vender_response = Filters().vendor_market_list(headers=sales_header, biz_type='seller')
        # 断言 vender_response 的 result 属性为 True
        assert vender_response['result'] == True, "The result of vendor_market_list should be True."
        # 断言 object 不为空,并且商家类型是seller的
        assert len(vender_response['object']) > 0, "The seller list should not be empty."
        for merchant in vender_response['object']:
            assert merchant['seller_type'] in [None, "mkpl,fbw",
                                               ""], f"Merchant {merchant['id']} does not have the correct seller_type."

        # 默认商品列表
        res = ProductsMgmt().admin_products(headers=sales_header, biz_type='seller',
                                            vender_id=vender_response["object"][14]["id"], rec_create_id='4208')
        assert res.get("result") is True, "API 请求没有返回成功结果"
        # 断言返回商品数量大于0小于20
        assert 0 < len(jmespath(res, 'object.list')) <= 20, f"seller default list return {res}"
        # 断言返回商品product_id没有重复,商家唯一且商品属于这个商家的
        product_id_lst = jmespath(res, 'object.list[*].product_id')
        vender_id_lst = jmespath(res, 'object.list[*].vender_id')
        vender_id_set = set(vender_id_lst)
        product_id_set = set(product_id_lst)
        current_vender_id = vender_response["object"][14]["id"]
        assert len(product_id_lst) == len(product_id_set), f'Found duplicate product_id: {product_id_lst}'
        assert len(vender_id_set) == 1, f'Found multiple vender IDs: {vender_id_lst}'
        assert int(current_vender_id) == int(
            list(vender_id_set)[0]), f'Product IDs do not match the vender ID: {current_vender_id}'
        audit_log = ProductsMgmt().product_audit_log(headers=sales_header,
                                                     product_id=res["object"]["list"][0]["product_id"])
        assert audit_log.get("result") is True and audit_log.get("object"), "API 请求没有返回成功结果或object为空"

        # 验证Name,Seller SKU,Weee SKU,Category,Please select,Brand,Please select,UPC,Product Origin,Please select,
        # SKU Create Time过滤商品
        res_catalogue = Filters().catalogue_tree(headers=sales_header)
        # 断言 result 属性为 True
        assert res_catalogue['result'] is True, "The result of catalogue_tree should be True."
        # 断言 object 中有 19 个大分类
        assert len(res_catalogue[
                       'object']) == 19, f"There should be exactly 19 main categories in object, found {len(res_catalogue['object'])}."

        res_brand = Filters().brand_list(headers=sales_header)
        # 断言 result 属性为 True
        assert res_brand['result'] is True, "The result of brand_list should be True."
        # 断言 object 中有 20 个品牌
        assert len(res_brand[
                       'object'][
                       'data']) == 20, f"There should be exactly 20 brands in object, found {len(res_brand['object']['data'])}."
        res_product_area = Filters().product_area(headers=sales_header)
        # 断言 result 属性为 True
        assert res_product_area['result'] is True, "The result of product_area should be True."
        # 断言 object 中有 78 个地区
        assert len(res_product_area[
                       'object']) == 78, f"There should be exactly 78 product area in object, found {len(res_product_area['object'])}."

        filter_res = ProductsMgmt().admin_products(headers=sales_header, biz_type='seller',
                                                   vender_id=vender_response["object"][14]["id"], name="123",
                                                   manufacturer_part_num="121", product_id="2119547",
                                                   catalogue_num=res_catalogue['object'][1],
                                                   brand_key="Ut2MvwIJ", upc_code="",
                                                   product_area=res_product_area['object'][0],
                                                   rec_create_id='4208')
        # 断言 result 属性为 True
        assert filter_res['result'] is True, "The result of filter_res should be True."

    @weeeTest.mark.list('sales', 'Transaction', 'MKPL', 'test_products_management')
    def test_global_fbw_products_list(self):
        """
        商品列表 Global FBW SKU tab
        """
        vender_response = Filters().vendor_market_list(headers=sales_header, biz_type='mkpl_fbw')
        # 断言 vender_response 的 result 属性为 True
        assert vender_response['result'] == True, "The result of vendor_market_list should be True."
        assert len(vender_response['object']) > 0, "The seller list should not be empty."
        for merchant in vender_response['object']:
            # 断言 open_global_fbw 是 True,商家都是GLobal+FBW商家
            assert merchant[
                       'open_global_fbw'] == True, f"Merchant {merchant['id']} does not have open_global_fbw set to True."
        # 默认商品列表
        res = ProductsMgmt().global_fbw_products_list(headers=sales_header, biz_type='mkpl_fbw',
                                            vender_id=vender_response["object"][14]["id"], rec_create_id='4208')
        assert res.get("result") is True, "API 请求没有返回成功结果"
        # 断言返回商品数量大于0小于20
        assert 0 < len(jmespath(res, 'object.list')) <= 20, f"seller default list return {res}"
        # 断言返回商品product_id没有重复,商家唯一且商品属于这个商家的
        product_id_lst = jmespath(res, 'object.list[*].product_id')
        vender_id_lst = jmespath(res, 'object.list[*].vender_id')
        vender_id_set = set(vender_id_lst)
        product_id_set = set(product_id_lst)
        current_vender_id = vender_response["object"][14]["id"]
        assert len(product_id_lst) == len(product_id_set), f'Found duplicate product_id: {product_id_lst}'
        assert len(vender_id_set) == 1, f'Found multiple vender IDs: {vender_id_lst}'
        assert int(current_vender_id) == int(
            list(vender_id_set)[0]), f'Product IDs do not match the vender ID: {current_vender_id}'
        audit_log = ProductsMgmt().product_audit_log(headers=sales_header,
                                                     product_id=res["object"]["list"][0]["product_id"])
        assert audit_log.get("result") is True and audit_log.get("object"), "API 请求没有返回成功结果或object为空"

    @weeeTest.mark.list('sales', 'Transaction', 'MKPL', 'test_products_management')
    def test_local_fbw_products_list(self):
        """
        商品列表 Local FBW SKU tab
        """
        vender_response = Filters().vendor_market_list(headers=sales_header, biz_type='fbw')
        # 断言 vender_response 的 result 属性为 True
        assert vender_response['result'] == True, "The result of vendor_market_list should be True."
        assert len(vender_response['object']) > 0, "The seller list should not be empty."
        for merchant in vender_response['object']:
            # # 断言 region_name 不为空，fbw商家配置售卖区域,有的商家可能没有售卖东西，就没有setup FBW
            # assert merchant['region_name'], f"Merchant {merchant['id']} has an empty region_name."
            # 断言 seller_type 包含 'fbw'，是fbw商家
            assert 'fbw' in merchant['seller_type'], f"Merchant {merchant['id']} does not have 'fbw' in seller_type."
        # 默认商品列表
        res = ProductsMgmt().admin_products(headers=sales_header, biz_type='fbw',
                                            vender_id=vender_response["object"][14]["id"], rec_create_id='4208')
        assert res.get("result") is True, "API 请求没有返回成功结果"
        # 断言返回商品数量大于0小于20
        assert 0 < len(jmespath(res, 'object.list')) <= 20, f"seller default list return {res}"
        # 断言返回商品product_id没有重复,商家唯一且商品属于这个商家的
        product_id_lst = jmespath(res, 'object.list[*].product_id')
        vender_id_lst = jmespath(res, 'object.list[*].vender_id')
        vender_id_set = set(vender_id_lst)
        product_id_set = set(product_id_lst)
        current_vender_id = vender_response["object"][14]["id"]
        assert len(product_id_lst) == len(product_id_set), f'Found duplicate product_id: {product_id_lst}'
        assert len(vender_id_set) == 1, f'Found multiple vender IDs: {vender_id_lst}'
        assert int(current_vender_id) == int(
            list(vender_id_set)[0]), f'Product IDs do not match the vender ID: {current_vender_id}'
        audit_log = ProductsMgmt().product_audit_log(headers=sales_header,
                                                     product_id=res["object"]["list"][0]["product_id"])
        assert audit_log.get("result") is True and audit_log.get("object"), "API 请求没有返回成功结果或object为空"


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    weeeTest.main()
