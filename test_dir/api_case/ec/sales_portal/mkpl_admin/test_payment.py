# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
sales portal MKPL 结算模块
"""
from datetime import datetime

import weeeTest
from weeeTest import weeeConfig, jmespath
from test_dir.api.ec.central_portal.central_mkpl import Payment
from . import sales_header


class TestMkplPayments(weeeTest.TestCase):
    """
    结算详情
    """

    @weeeTest.mark.list('sales', 'Transaction', 'MKPL', 'test_payment')
    def test_settlement_dashboard(self):
        """
        sales MKPL 结算看板
        """
        dashboard = Payment().mkpl_payment_status_list(headers=sales_header)
        for item in dashboard['object']['data']:
            # 验证 actual_amount 和 total_amount 是否为数字
            assert isinstance(item['actual_amount'],
                              (int, float)), f"actual_amount is not a number: {item['actual_amount']}"
            assert isinstance(item['total_amount'],
                              (int, float)), f"total_amount is not a number: {item['total_amount']}"

            # 验证 bill_end_time 和 bill_start_time 是否可以转换为有效的时间格式
            try:
                datetime.strptime(item['bill_start_time'], "%Y-%m-%d %H:%M:%S")
            except ValueError:
                assert False, f"bill_start_time is not a valid datetime: {item['bill_start_time']}"

            try:
                datetime.strptime(item['bill_end_time'], "%Y-%m-%d %H:%M:%S")
            except ValueError:
                assert False, f"bill_end_time is not a valid datetime: {item['bill_end_time']}"

            # 验证 seller_name 是否为非空字符串
            assert isinstance(item['seller_name'], str) and item[
                'seller_name'], f"seller_name is not a non-empty string: {item['seller_name']}"

            # 验证 id 是否为整数且不等于5044
            assert isinstance(item['id'], int), f"id is not an integer: {item['id']}"
            assert item['id'] != 5044, f"id is equal to 5044: {item['id']}"

    @weeeTest.mark.list('sales', 'Transaction', 'MKPL', 'test_payment')
    def test_mkpl_seller_settlement(self):
        """
        global+ 商家结算单
        """
        dashboard = Payment().mkpl_payment_status_list(headers=sales_header)
        seller_id = jmespath(dashboard, 'object.data[0].seller_id')
        summary_id = jmespath(dashboard, 'object.data[0].id')
        seller_settlement = Payment().seller_settlement(headers=sales_header, vendor_id=seller_id, biz_type='MKPL')
        for item in seller_settlement['object']:
            # 验证 bill_end_time 和 bill_start_time 是否可以转换为有效的时间格式
            try:
                datetime.strptime(item['transaction_start_time'], "%Y-%m-%d %H:%M:%S")
            except ValueError:
                assert False, f"transaction_start_time is not a valid datetime: {item['bill_start_time']}"

            try:
                datetime.strptime(item['transaction_end_time'], "%Y-%m-%d %H:%M:%S")
            except ValueError:
                assert False, f"transaction_end_time is not a valid datetime: {item['bill_end_time']}"
                # 验证 seller_name 是否为非空字符串
            assert isinstance(item['payout_status'], str) and item[
                'payout_status'], f"payout_status is not a non-empty string: {item['payout_status']}"

    @weeeTest.mark.list('sales', 'Transaction', 'MKPL', 'test_payment')
    def test_mkpl_seller_settlement_detail(self):
        """
        global+ 商家结算详情
        """
        dashboard = Payment().mkpl_payment_status_list(headers=sales_header)
        seller_id = jmespath(dashboard, 'object.data[0].seller_id')
        summary_id = jmespath(dashboard, 'object.data[0].id')
        settlement_detail = Payment().seller_settlement_detail(headers=sales_header, summary_id=summary_id,
                                                               vendor_id=seller_id, biz_type="MKPL")
        # 断言以 'mkpl' 开头的键对应的值都是数字
        mkpl_keys = [key for key in settlement_detail.get('object', {}) if key.startswith('mkpl')]

        for key in mkpl_keys:
            value = settlement_detail['object'][key]
            assert isinstance(value, (int, float)), f"Value for key '{key}' is not a number."
