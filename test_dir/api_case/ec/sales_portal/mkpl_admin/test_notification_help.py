# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
sales portal MKPL Seller管理模块
"""
import weeeTest
import re
from weeeTest import weeeConfig, jmespath
from . import sales_header
from test_dir.api.ec.central_portal.central_mkpl import NotificationHelp


class TestNotification(weeeTest.TestCase):
    """
    系统通知
    """

    @weeeTest.mark.list('sales', 'Transaction', 'MKPL', 'test_notification_help')
    def test_notification_list(self):
        """
        MKPL admin系统通知
        """
        notification_list = NotificationHelp().admin_announcements(headers=sales_header)
        # 断言 'object' 和 'list' 键是否存在
        assert 'object' in notification_list, "'object' key not found in the response"
        object_data = notification_list['object']
        assert 'list' in object_data, "'list' key not found in the object"

        # 'list' 为一个列表
        assert isinstance(object_data['list'], list), "'list' should be a list"

        # 遍历 'list' 列表中的每个数据
        for item in object_data['list']:
            # 断言 'title' 是非空字符串
            assert isinstance(item['title'], str) and item['title'].strip(), "'title' should be a non-empty string"
            # 断言 'body' 是以 <p> 开始以 </p> 结束的字符串
            assert isinstance(item['body'], str), "'body' should be a string"
            assert re.match(r'^<p>.*</p>$', item['body'].strip()), "'body' should start with '<p>' and end with '</p>'"

    @weeeTest.mark.list('sales', 'Transaction', 'MKPL', 'test_notification_help')
    def test_notification_detail(self):
        """
        MKPL admin系统通知详情
        """
        notification_list = NotificationHelp().admin_announcements(headers=sales_header)
        announcement_id = jmespath(notification_list, 'object.list[0].announcement_id')
        detail = NotificationHelp().admin_announcement_detail(headers=sales_header, announcement_id=announcement_id)
        announcement = detail['object']

        # 断言 title 为非空字符串
        assert isinstance(announcement['title'], str) and announcement[
            'title'].strip(), f"announcement {announcement_id} title is not a non-empty string"

        # 断言 body 为以 <p> 开头并以 </p> 结尾的字符串
        assert announcement['body'].startswith('<p>') and announcement['body'].endswith(
            '</p>'), f"announcement {announcement_id} body does not start with '<p>' or end with '</p>'"


class TestHelpCenter(weeeTest.TestCase):
    """
    帮助中心
    """

    @weeeTest.mark.list('sales', 'Transaction', 'MKPL')
    def test_help_center_list(self):
        """
        MKPL admin 帮助中心文档
        """
        doc_list = NotificationHelp().help_doc_list(headers=sales_header)
        assert isinstance(jmespath(doc_list, 'object.data'), list), "'object.data' should be a list"

        # 遍历 'list' 列表中的每个条目
        for item in jmespath(doc_list, 'object.data'):
            # 断言 'title' 是非空字符串
            assert isinstance(item['title'], str) and item['title'].strip(), "'title' should be a non-empty string"
            # 断言 'content' 是以 <p> 开始的字符串
            assert isinstance(item['content'], str), "'body' should be a string"
            # assert re.match(r'^<p>.*</p>$',
            #                 item['content'].strip()), "'body' should start with '<p>' and end with '</p>'"
            # 帮助文档以表格结尾的可能是</table></div>标签 不是p标签
            assert re.match(r'^<p',
                            item['content'].strip()), "'body' should start with '<p>'"
