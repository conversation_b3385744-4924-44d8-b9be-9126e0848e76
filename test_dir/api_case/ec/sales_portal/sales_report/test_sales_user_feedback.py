# !/usr/bin/python3
# -*- coding: utf-8 -*-


import weeeTest

from test_dir.api.ec.central_portal.central_growth import CentralGrowth
from test_dir.api.ec.central_portal.central_recommend import CentralRecommend


class TestSalesUserFeedback(weeeTest.TestCase):
    @weeeTest.mark.list('sales', 'Transaction', 'test_sales_product_discovery')
    def test_sales_user_feedback(self, sales_header):
        """ # 报表-用户反馈-查询用户反馈页面数据 """
        type =["appStore","googlePlay","nps"]
        for type in type:
            user_feedback = CentralGrowth().feedback_detail_statistics(headers=sales_header,type=type)
            assert user_feedback["object"]["classify_count_map"] is not None, f'查询用户反馈页面数据数据异常{user_feedback}'
            assert user_feedback["object"]["classify_percentage_map"] is not None, f'查询用户反馈页面数据数据异常{user_feedback}'
            assert user_feedback["object"]["grade_count_map"] is not None, f'查询用户反馈页面数据数据异常{user_feedback}'
            assert user_feedback["object"]["grade_percentage_map"] is not None, f'查询用户反馈页面数据数据异常{user_feedback}'
            assert user_feedback["object"]["rating_count_map"] is not None, f'查询用户反馈页面数据数据异常{user_feedback}'
            assert user_feedback["object"]["rating_percentage_map"] is not None, f'查询用户反馈页面数据数据异常{user_feedback}'

        # 报表-产品发现-查询产品发现Daily页面数据
        daily_total = CentralGrowth().feedback_detail_items(headers=sales_header, tab="Daily")
        assert len(daily_total["object"]) > 0, f'查询报表-产品发现-查询产品发现Daily页面数据异常{daily_total}'
