# !/usr/bin/python3
# -*- coding: utf-8 -*-


import weeeTest
from test_dir.api.ec.central_portal.central_im import CentralIm


class TestSalesProductReport(weeeTest.TestCase):
    @weeeTest.mark.list('sales', 'Transaction', 'test_sales_product_performance_v1_search')
    def test_sales_product_performance_v1_search(self, sales_header):
        """ # 报表-商品指标看板-查询商品指标看板页面数据 """
        product_search = CentralIm().product_performance_v1_search(headers=sales_header)
        assert product_search["success"] is True, f'查询Promotion list页面数据异常{product_search}'
        # mod = product_search["body"]["mod"]
        for mod in product_search["body"]["mod"]:
            # mods = mod["mods"]
            # page_key = mod["page_key"]
            for mods in mod["mods"]:
                # mod_key = mods["mod_key"]
                product_search_filter = CentralIm().product_performance_v1_search(headers=sales_header,
                                                                                  page_key=mod["page_key"],
                                                                                  mod_key=mods["mod_key"])
                assert product_search_filter["success"] is True, f'查询商品指标看板页面数据异常{product_search_filter}'
