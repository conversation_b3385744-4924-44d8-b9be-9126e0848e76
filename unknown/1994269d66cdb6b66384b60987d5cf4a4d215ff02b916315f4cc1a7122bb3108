# PC首页进入category
# 各category搜索结果总div
ele_category_result_div = "div[class*='DiscoveryProducts_listContent']"

# 各具体product div
ele_category_result_product_div = "div[class*='DiscoveryProducts_resultItem']"

# category加购按钮
ele_category_add_to_cart = "div[class*='DiscoveryProducts_resultItem'] i[data-role='addButtonPlusIcon']"

# 分类页右侧分类
Alcohol = u"//div[contains(@class,'SlideCategoriesList_rightSearchCol')]//a[@aria-label='Alcohol']"
ele_filter_reset = u"//span[@data-testid='btn-sort-filter-reset']"

local_delivery_test_id = "btn-delivery_type-delivery_type_local"
pantry_delivery_test_id = "btn-delivery_type-delivery_type_pantry"
global_delivery_test_id = "btn-delivery_type-delivery_type_global"